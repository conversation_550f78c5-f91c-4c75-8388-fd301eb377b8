spring:
  main:
    allow-bean-definition-overriding: true
  profiles:
    active: dev
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
      enabled: true
  mvc:
    throw-exception-if-no-handler-found: true
    static-path-pattern: /static/**
  web:
    resources:
      add-mappings: false

# mybatis-plus配置
mybatis-plus:
  # mapper文件的扫描路径
  mapper-locations: classpath*:/mapper/**/*.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.tjsj.**.modules.**.model
  # 全局配置
  global-config:
    banner: false
    # 表主键默认自增类型
    db-config:
      id-type: auto
  # 原生配置
  configuration:
    # 下划线转驼峰
    map-underscore-to-camel-case: true
    cache-enabled: false
    # 指定当查询结果中的字段值为 NULL 时，调用对应实体类的 setter 方法
    call-setters-on-nulls: true
    # mybatis-plus日志打印实现类
    # log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    # 驼峰下划线转换，默认Partial,开启后，会将数据库字段下划线命名转换为驼峰命名,比如user_name转换为userName
    auto-mapping-behavior: full
    default-enum-type-handler: org.apache.ibatis.type.EnumTypeHandler


# jasypt加密配置，用来加密和解密配置文件中的敏感信息
jasypt:
  encryptor:
    password: yky2000  # 这是用来加密和解密的密钥
    algorithm: PBEWithMD5AndDES  # 加密算法