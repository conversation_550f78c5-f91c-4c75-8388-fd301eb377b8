package com.tjsj.modules.sync.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.tjsj.common.annotation.CheckCount;
import com.tjsj.common.annotation.sync.CronSyncConfig;
import com.tjsj.common.enums.EnvironmentTypeEnum;
import com.tjsj.common.enums.base.CommonStatus;
import com.tjsj.common.enums.sync.SyncTaskTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.experimental.FieldNameConstants;
import org.apache.ibatis.type.Alias;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * SyncTableConfigDO
 *
 * <AUTHOR>
 * @date 2024/08/07
 * @description 数据库同步表
 */
@TableName(value = "tarkin.t_sync_table_config")
@Data
@Accessors(chain = true)
@Alias(value = "SyncTableConfigDO")
@Schema(name = "SyncTableConfigDO", description = "数据库同步表")
@CheckCount(count = 1)
@FieldNameConstants
public class SyncTableConfigDO implements Serializable {

    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 同步库名称
     */
    @Schema(description = "同步库名称")
    @TableField(value = "schema_name")
    @CronSyncConfig
    private String schemaName;

    /**
     * 同步表名称
     */
    @Schema(description = "同步表名称")
    @TableField(value = "table_name")
    @CronSyncConfig
    private String tableName;

    /**
     * 数据来源的数据库名
     */
    @Schema(description = "数据来源的数据库名")
    @TableField(value = "data_schema_name")
    @CronSyncConfig
    private String dataSchemaName;

    /**
     * 数据来源的表名
     */
    @Schema(description = "数据来源的表名")
    @TableField(value = "data_table_name")
    @CronSyncConfig
    private String dataTableName;

    /**
     * 表注释
     */
    @Schema(description = "表注释")
    @TableField(value = "table_comment")
    private String tableComment;

    /**
     * 启用状态
     */
    @Schema(description = "启用状态")
    @TableField(value = "enable_status")
    private CommonStatus enableStatus;

    /**
     * 项目id
     */
    @Schema(description = "项目id")
    @TableField(value = "project_id")
    @CronSyncConfig
    private String projectId;

    /**
     * 数据库类型
     */
    @Schema(description = "数据库类型")
    @TableField(value = "db_type")
    @CronSyncConfig
    private EnvironmentTypeEnum dbType;

    /**
     * 环境配置
     */
    @Schema(description = "环境配置")
    @TableField(value = "profile_type")
    private String profileType;

    /**
     * 是否全量更新
     */
    @Schema(description = "是否全量更新")
    @TableField(value = "if_full_update")
    @CronSyncConfig
    private CommonStatus ifFullUpdate;

    /**
     * 每批次读取数据量
     */
    @Schema(description = "每批次读取数据量")
    @TableField(value = "read_size")
    @CronSyncConfig
    private Integer readSize;

    /**
     * 每批次同步数量
     */
    @Schema(description = "每批次同步数量")
    @TableField(value = "batch_size")
    @CronSyncConfig
    private Integer batchSize;

    /**
     * 删除状态
     */
    @Schema(description = "删除状态")
    @TableField(value = "delete_status")
    private CommonStatus deleteStatus;

    /**
     * 同步的列，多个用,分割（如果为空，默认同步所有列）
     */
    @Schema(description = "同步的列，多个用,分割（如果为空，默认同步所有列）")
    @TableField(value = "include_column")
    @CronSyncConfig
    private String includeColumn;

    /**
     * 排除的列，多个用,分割
     */
    @Schema(description = "排除的列，多个用,分割")
    @TableField(value = "exclude_column")
    @CronSyncConfig
    private String excludeColumn;

    /**
     * 任务类型
     */
    @Schema(description = "任务类型")
    @TableField(value = "task_type")
    @CronSyncConfig
    private SyncTaskTypeEnum taskType;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @TableField(value = "remark")
    private String remark;

    /**
     * crontab表达式
     */
    @Schema(description = "crontab表达式")
    @TableField(value = "crontab")
    @CronSyncConfig
    private String crontab;

    /**
     * 表同步顺序
     */
    @Schema(description = "表同步顺序")
    @TableField(value = "sync_order")
    @CronSyncConfig
    private Integer syncOrder;

    /**
     * 数据源表最大记录时间
     */
    @Schema(description = "数据源表最大记录时间")
    @TableField(value = "source_max_time")
    @Deprecated
    private LocalDateTime sourceMaxTime;

    /**
     * 目标表最大记录时间
     */
    @Schema(description = "目标表最大记录时间")
    @TableField(value = "target_max_time")
    @Deprecated
    private LocalDateTime targetMaxTime;

    /**
     * 是否通过比较最大记录进行更新
     */
    @Schema(description = "是否通过比较最大记录进行更新")
    @TableField(value = "if_compare_max_time")
    @CronSyncConfig
    private CommonStatus ifCompareMaxTime;

    /**
     * 是否正在同步：0-正在同步，1-未在同步中
     */
    @Schema(description = "是否正在同步：0-正在同步，1-未在同步中")
    @TableField(value = "if_syncing")
    private CommonStatus ifSyncing;

    /**
     * 最后同步开始时间
     */
    @Schema(description = "最后同步开始时间")
    @TableField(value = "last_sync_start_time")
    private LocalDateTime lastSyncStartTime;

    /**
     * 表分组字段，用来在表数据修复方法中，对表数据进行分组
     */
    @Schema(description = "表分组字段")
    @TableField(value = "table_group_by_column")
    @CronSyncConfig
    private String tableGroupByColumn;

    /**
     * Quartz定时任务上次同步数据hash值
     */
    @Schema(description = "Quartz定时任务上次同步数据hash值")
    @TableField(value = "quartz_last_sync_data_hash")
    private String quartzLastSyncDataHash;

    /**
     *
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     *
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @Serial
    private static final long serialVersionUID = 1L;

}