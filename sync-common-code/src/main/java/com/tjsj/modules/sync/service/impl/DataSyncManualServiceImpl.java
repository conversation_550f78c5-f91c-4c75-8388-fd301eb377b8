package com.tjsj.modules.sync.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tjsj.modules.sync.mapper.DataSyncManualMapper;
import com.tjsj.modules.sync.model.entity.DataSyncManualDO;
import com.tjsj.modules.sync.service.DataSyncManualService;
import com.tjsj.modules.manage.service.HeartBeatTestService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/7/17 23:03
 * @description
 */

@Service
public class DataSyncManualServiceImpl extends ServiceImpl<DataSyncManualMapper, DataSyncManualDO>
        implements DataSyncManualService {
    @Resource
    private HeartBeatTestService heartBeatTestService;


}
