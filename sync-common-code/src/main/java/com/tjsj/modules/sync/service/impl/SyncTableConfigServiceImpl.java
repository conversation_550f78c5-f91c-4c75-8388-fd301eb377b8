package com.tjsj.modules.sync.service.impl;

import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tjsj.common.config.TarkinConfig;
import com.tjsj.common.enums.EnvironmentTypeEnum;
import com.tjsj.common.enums.base.CommonStatus;
import com.tjsj.modules.sync.mapper.SyncTableConfigMapper;
import com.tjsj.modules.sync.model.entity.SyncTableConfigDO;
import com.tjsj.modules.sync.model.request.SyncDataRequest;
import com.tjsj.modules.sync.model.vo.SyncTableConfigVO;
import com.tjsj.modules.sync.service.SyncTableConfigService;
import com.tjsj.common.utils.data.MyPageInfo;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * SyncTableConfigServiceImpl
 *
 * <AUTHOR> Ye
 * @date 2024/08/07
 * @description 针对表【database_sync_tables】的数据库操作Service实现
 */
@Service
public class SyncTableConfigServiceImpl extends ServiceImpl<SyncTableConfigMapper, SyncTableConfigDO>
        implements SyncTableConfigService {

    @Resource
    private TarkinConfig tarkinConfig;

    @Override
    public MyPageInfo<SyncTableConfigVO> querySyncTableConfigs(SyncDataRequest request) {
        Page<SyncTableConfigVO> page = PageHelper.startPage(request.getCurrent(), request.getSize());
        request.setOrderBy(SyncDataRequest.camelToUnderline(request.getOrderBy()));

        List<SyncTableConfigVO> list = this.baseMapper.querySyncTableConfigs(request);
        return MyPageInfo.buildPageInfo(page, list);
    }

    @Override
    public List<SyncTableConfigDO> findRecentlyUpdatedConfig(LocalDateTime lastSyncTime) {

        return this.list(Wrappers.<SyncTableConfigDO>lambdaQuery()
                        .eq(SyncTableConfigDO::getProjectId, tarkinConfig.getProjectId())
                        .eq(SyncTableConfigDO::getDbType, tarkinConfig.getDbType())
                        .eq(SyncTableConfigDO::getProfileType, tarkinConfig.getProfile())
                        .isNotNull(SyncTableConfigDO::getCrontab)
                        .ne(SyncTableConfigDO::getCrontab, "")
                        .gt(lastSyncTime != null, SyncTableConfigDO::getUpdateTime, lastSyncTime))
                .stream()
                .filter(config -> StrUtil.isNotBlank(config.getCrontab()))
                .toList();
    }

    @Override
    public Boolean checkTableSyncable(Integer tableConfigId) {

        // 如果1.表配置当前未在同步，或者2.最后一次同步时间超过10小时，则可以进行同步

        SyncTableConfigDO syncTableConfigDO = this.getById(tableConfigId);
        CommonStatus ifSyncing = syncTableConfigDO.getIfSyncing();
        LocalDateTime lastSyncStartTime = syncTableConfigDO.getLastSyncStartTime();
        return ifSyncing == CommonStatus.DISABLE ||
                (lastSyncStartTime != null && lastSyncStartTime.isBefore(LocalDateTime.now().minusHours(10)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void generateSyncTableConfig(String sourceDbType, String targetDbType, String sourceProfileType,
                                        String targetProfileType) {

        List<SyncTableConfigDO> sourceTableConfigList = this.list(Wrappers.<SyncTableConfigDO>lambdaQuery()
                .eq(SyncTableConfigDO::getDbType, sourceDbType)
                .eq(SyncTableConfigDO::getProfileType, sourceProfileType)
                .eq(SyncTableConfigDO::getDeleteStatus, CommonStatus.ENABLE));

        sourceTableConfigList.forEach(sourceTableConfig -> {
            sourceTableConfig.setDbType(EnumUtil.getBy(EnvironmentTypeEnum::getCode, targetDbType))
                    .setProfileType(targetProfileType)
                    .setId(null)
                    .setCreateTime(null)
                    .setUpdateTime(null)
                    .setQuartzLastSyncDataHash(null)
                    .setLastSyncStartTime(null)
                    .setIfSyncing(CommonStatus.DISABLE);
        });

        this.saveBatch(sourceTableConfigList);

    }


}




