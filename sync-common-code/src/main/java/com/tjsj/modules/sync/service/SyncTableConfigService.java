package com.tjsj.modules.sync.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tjsj.modules.sync.model.entity.SyncTableConfigDO;
import com.tjsj.modules.sync.model.request.SyncDataRequest;
import com.tjsj.modules.sync.model.vo.SyncTableConfigVO;
import com.tjsj.common.utils.data.MyPageInfo;

import java.time.LocalDateTime;
import java.util.List;

/**
 * SyncTableConfigService
 *
 * <AUTHOR>
 * @date 2024/08/07
 * @description 针对表【database_sync_tables】的数据库操作Service
 */
public interface SyncTableConfigService extends IService<SyncTableConfigDO> {

    /**
     * 查询同步表配置
     *
     * @param request 请求
     * @return {@link MyPageInfo }<{@link List }<{@link SyncTableConfigVO }>>
     * <AUTHOR>
     * @date 2024/08/08
     */
    MyPageInfo<SyncTableConfigVO> querySyncTableConfigs(SyncDataRequest request);

    /**
     * 查找最近更新配置
     *
     * @param lastSyncTime 上次同步时间
     * @return {@link List }<{@link SyncTableConfigDO }>
     * <AUTHOR> Ye
     * @date 2024/12/17
     */
    List<SyncTableConfigDO> findRecentlyUpdatedConfig(LocalDateTime lastSyncTime);

    /**
     * 检查表是否可同步
     *
     * @param tableConfigId 表配置id
     * @return  {@link Boolean }
     * <AUTHOR> Ye
     * @date 2025/04/15
     */
    Boolean checkTableSyncable(Integer tableConfigId);

    /**
     * 产生同时表配置
     *
     * @param sourceDbType 源数据库类型
     * @param targetDbType 目标数据库类型
     * @param sourceProfileType 源配置文件类型
     * @param targetProfileType 目标配置文件类型
     *
     * <AUTHOR> Ye
     * @date 2025/06/30
     */
    void generateSyncTableConfig(String sourceDbType, String targetDbType, String sourceProfileType,
                                 String targetProfileType);
}
