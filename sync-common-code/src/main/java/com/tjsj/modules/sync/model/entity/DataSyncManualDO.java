package com.tjsj.modules.sync.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.tjsj.common.annotation.CheckCount;
import com.tjsj.common.enums.EnvironmentTypeEnum;
import com.tjsj.common.enums.TaskExecStatus;
import com.tjsj.common.enums.base.CommonStatus;
import com.tjsj.common.enums.sync.SyncTaskTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.Alias;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * DataSyncManualDO
 *
 * <AUTHOR>
 * @date 2024/7/17 23:03
 * @description
 */
@Data
@Accessors(chain = true)
@TableName(value = "tarkin.t_data_sync_manual")
@Alias(value = "DataSyncManualDO")
@Schema(description = "数据库同步指定表")
@CheckCount(count = 1)
@Deprecated
public class DataSyncManualDO implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "")
    private Integer id;

    /**
     * 执行语句
     */
    @TableField(value = "execute_sql")
    @Schema(description = "执行语句")
    private String executeSql;


    @TableField(value = "db_type")
    @Schema(description = "数据库类型")
    private EnvironmentTypeEnum dbType;

    /**
     * 被同步表名（包括数据库名）
     */
    @TableField(value = "delete_table_name")
    @Schema(description = "被同步表名（包括数据库名）")
    private String deleteTableName;

    /**
     * 同步表名
     */
    @TableField(value = "insert_table_name")
    @Schema(description = "同步表名")
    private String insertTableName;

    /**
     * 删除表条件
     */
    @TableField(value = "delete_table_condition")
    @Schema(description = "删除表条件")
    private String deleteTableCondition;

    /**
     * 插入表条件
     */
    @TableField(value = "insert_table_condition")
    @Schema(description = "插入表条件")
    private String insertTableCondition;

    /**
     * 删除起始id
     */
    @TableField(value = "delete_start_id")
    @Schema(description = "删除起始id")
    private Integer deleteStartId;

    @TableField(value = "insert_start_id")
    @Schema(description = "插入起始id")
    private Integer insertStartId;

    @TableField(value = "delete_start_update_time")
    @Schema(description = "删除起始更新时间")
    private LocalDateTime deleteStartUpdateTime;

    @TableField(value = "insert_start_update_time")
    @Schema(description = "插入起始更新时间")
    private LocalDateTime insertStartUpdateTime;

    /**
     * 是否同步ID
     */
    @TableField(value = "if_id_sync")
    @Schema(description = "是否同步ID", type = "integer")
    private CommonStatus ifIdSync;


    /**
     * 是否删除被同步表
     */
    @TableField(value = "if_table_delete")
    @Schema(description = "是否删除被同步表")
    private CommonStatus ifTableDelete;

    /**
     * 任务状态
     */
    @TableField(value = "task_status")
    @Schema(description = "任务状态")
    private TaskExecStatus taskStatus;

    @TableField(value = "task_type")
    @Schema(description = "任务类型")
    private SyncTaskTypeEnum taskType;


    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @Schema(description = "")
    private LocalDateTime createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "")
    private LocalDateTime updateTime;

    @Serial
    private static final long serialVersionUID = 1L;
}