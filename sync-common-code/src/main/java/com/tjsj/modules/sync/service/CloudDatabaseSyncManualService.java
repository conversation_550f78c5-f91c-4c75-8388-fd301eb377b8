package com.tjsj.modules.sync.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tjsj.modules.sync.model.entity.CloudDataSyncManualDO;

/**
 * CloudDatabaseSyncManualService
 *
 * <AUTHOR> Ye
 * @date 2024/08/07
 * @description 针对表【t_database_sync_info(数据库同步指定表)】的数据库操作Service
 */

public interface CloudDatabaseSyncManualService extends IService<CloudDataSyncManualDO> {

    /**
     * 获取记录
     *
     * @param localDatabaseSyncManual 本地数据库同步手动
     * @return {@link CloudDataSyncManualDO }
     * <AUTHOR> Ye
     * @date 2024/10/29
     */
    CloudDataSyncManualDO getOne(CloudDataSyncManualDO localDatabaseSyncManual);
}
