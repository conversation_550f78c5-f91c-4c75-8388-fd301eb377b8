package com.tjsj.modules.sync.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tjsj.modules.sync.model.entity.DataSyncManualDO;
import com.tjsj.modules.sync.model.request.SyncDataRequest;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * DataSyncManualMapper
 *
 * <AUTHOR>
 * @date 2024/7/17 23:03
 * @description
 */

@Mapper
public interface DataSyncManualMapper extends BaseMapper<DataSyncManualDO> {

}