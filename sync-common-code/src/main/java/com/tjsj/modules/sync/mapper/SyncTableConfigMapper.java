package com.tjsj.modules.sync.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tjsj.datasource.DataSourceNames;
import com.tjsj.modules.sync.model.entity.SyncTableConfigDO;
import com.tjsj.modules.sync.model.request.SyncDataRequest;
import com.tjsj.modules.sync.model.vo.SyncTableConfigVO;
import com.tjsj.common.utils.data.MyPageInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * SyncTableConfigMapper
 *
 * <AUTHOR>
 * @date 2024/08/07
 * @description 针对表【database_sync_tables】的数据库操作Mapper
 */
@Mapper
@DS(DataSourceNames.SOURCE_DB)
public interface SyncTableConfigMapper extends BaseMapper<SyncTableConfigDO> {

    /**
     * 查询同步表配置
     *
     * @param request 请求
     * @return {@link MyPageInfo }<{@link SyncTableConfigDO }>
     * <AUTHOR> Ye
     * @date 2024/08/08
     */
    List<SyncTableConfigVO> querySyncTableConfigs(@Param("request") SyncDataRequest request);


}




