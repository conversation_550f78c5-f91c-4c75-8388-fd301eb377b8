package com.tjsj.modules.log.service.impl;

import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tjsj.common.config.TarkinConfig;
import com.tjsj.common.enums.EnvironmentTypeEnum;
import com.tjsj.common.enums.sync.SyncTypeEnum;
import com.tjsj.common.utils.date.DateUtils;
import com.tjsj.datasource.DataSourceNames;
import com.tjsj.modules.log.mapper.DatabaseSyncHistoryMapper;
import com.tjsj.modules.log.model.entity.DatabaseSyncHistoryDO;
import com.tjsj.modules.log.service.DatabaseSyncHistoryService;
import com.tjsj.modules.sync.model.entity.CloudDataSyncManualDO;
import com.tjsj.modules.sync.model.entity.SyncTableConfigDO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * DatabaseSyncHistoryServiceImpl
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/11/26
 * @description 数据同步历史记录服务实现
 */
@Service
@DS(value = DataSourceNames.SOURCE_DB)
public class DatabaseSyncHistoryServiceImpl extends ServiceImpl<DatabaseSyncHistoryMapper, DatabaseSyncHistoryDO>
        implements DatabaseSyncHistoryService {

    @Resource
    private TarkinConfig tarkinConfig;


    @Override
    public DatabaseSyncHistoryDO createSyncHistoryRecord(Integer taskId, CloudDataSyncManualDO syncManual,
                                                         SyncTableConfigDO tableConfig, SyncTypeEnum syncTypeEnum) {

        DatabaseSyncHistoryDO databaseSyncHistory = new DatabaseSyncHistoryDO()
                .setDate(DateUtils.format(new Date(), DateUtils.DATE_PATTERN))
                .setStartTime(LocalDateTime.now())
                .setProjectId(tableConfig.getProjectId())
                .setDbType(EnumUtil.getBy(EnvironmentTypeEnum::getCode, tarkinConfig.getDbType()))
                .setProfileType(tarkinConfig.getProfile())
                .setSyncType(syncTypeEnum != null ?
                        syncTypeEnum : (syncManual != null ? SyncTypeEnum.MANUAL : SyncTypeEnum.AUTO))
                .setTaskType(syncManual != null ? syncManual.getTaskType() : tableConfig.getTaskType())
                .setTaskId(taskId);

        if (syncManual != null) {
            // 设置插入表名
            String insertTableName = syncManual.getInsertTableName();
            if (StrUtil.isNotEmpty(insertTableName)) {
                // 取出数据库名和表名
                String[] split = insertTableName.split("\\.");
                String databaseName = split[0];
                String tableName = split[1];
                databaseSyncHistory.setDataSchemaName(databaseName)
                        .setDataTableName(tableName);
                if (StrUtil.isNotEmpty(syncManual.getDeleteTableName())) {
                    String[] deleteSplit = syncManual.getDeleteTableName().split("\\.");
                    String deleteDatabaseName = deleteSplit[0];
                    String deleteTableName = deleteSplit[1];
                    databaseSyncHistory.setSchemaName(deleteDatabaseName)
                            .setTableName(deleteTableName);
                }
            } else if (StrUtil.isNotEmpty(syncManual.getExecuteSql())) {
                // 将执行语句设置到同步历史日志中
                databaseSyncHistory.setExecuteSql(syncManual.getExecuteSql());
            }
        } else {
            databaseSyncHistory.setSchemaName(tableConfig.getSchemaName())
                    .setTableName(tableConfig.getTableName())
                    .setDataSchemaName(tableConfig.getDataSchemaName())
                    .setDataTableName(tableConfig.getDataTableName());
        }


        return databaseSyncHistory;
    }
}




