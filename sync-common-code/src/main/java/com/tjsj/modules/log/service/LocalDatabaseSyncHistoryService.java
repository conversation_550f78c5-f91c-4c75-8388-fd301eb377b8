package com.tjsj.modules.log.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tjsj.common.enums.sync.SyncTypeEnum;
import com.tjsj.modules.log.model.entity.DatabaseSyncHistoryDO;
import com.tjsj.modules.sync.model.entity.DataSyncManualDO;
import com.tjsj.modules.sync.model.entity.SyncTableConfigDO;

/**
 * LocalDatabaseSyncHistoryService
 *
 * <AUTHOR>
 * @date 2024/07/09
 * @version 1.0.0
 * @description 针对表【t_database_sync_history(同步数据库日志表)】的数据库操作Service
 */
public interface LocalDatabaseSyncHistoryService extends IService<DatabaseSyncHistoryDO> {



    /**
     * 创建同步历史记录
     *
     * @param taskId       任务id
     * @param syncManual   同步手动
     * @param tableConfig  表配置
     * @param syncTypeEnum 同步类型
     * @return {@link DatabaseSyncHistoryDO }
     * <AUTHOR> Ye
     * @date 2024/11/26
     */
    DatabaseSyncHistoryDO createSyncHistoryRecord(Integer taskId,
                                                  DataSyncManualDO syncManual,
                                                  SyncTableConfigDO tableConfig, SyncTypeEnum syncTypeEnum);
}
