package com.tjsj.modules.log.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tjsj.common.enums.sync.SyncTypeEnum;
import com.tjsj.modules.log.model.entity.DatabaseSyncHistoryDO;
import com.tjsj.modules.sync.model.entity.DataSyncManualDO;
import com.tjsj.modules.sync.model.entity.SyncTableConfigDO;

/**
 * DatabaseSyncHistoryService
 *
 * <AUTHOR>
 * @date 2024/07/09
 * @description 针对表【t_database_sync_history(同步数据库日志表)】的数据库操作Service
 */
public interface DatabaseSyncHistoryService extends IService<DatabaseSyncHistoryDO> {



    /**
     * 创建同步历史记录
     *
     * @param taskId       任务id
     * @param syncManual   同步手动
     * @param tableConfig  表配置
     * @param syncTypeEnum 同步类型
     * @return {@link DatabaseSyncHistoryDO }
     * <AUTHOR>
     * @date 2024/11/26
     */
    DatabaseSyncHistoryDO createSyncHistoryRecord(Integer taskId,
                                                  DataSyncManual<PERSON><PERSON> syncManual,
                                                  SyncTableConfigDO tableConfig, SyncTypeEnum syncTypeEnum);
}
