package com.tjsj.common.utils.data;

import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * HashUtil
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/5/21 16:18
 * @description HashUtil
 */
@Slf4j
public class HashUtil {

	/**
	 * 生成哈希值
	 *
	 * @param list 输入的Map列表
	 * @return 哈希值
	 * <AUTHOR>
	 * @date 2025/05/21
	 */
	public static String generateHash(List<Map<String, Object>> list) {

		StringBuilder combinedString = new StringBuilder();

		for (Map<String, Object> map : list) {
//			combinedString.append(mapToSortedStringExcludeTimeFields(map)).append("|");
			combinedString.append(mapToSortedString(map)).append("|");
		}

		try {
			MessageDigest digest = MessageDigest.getInstance("SHA-256");
			byte[] hashBytes = digest.digest(combinedString.toString().getBytes(StandardCharsets.UTF_8));

			return bytesToHex(hashBytes);

		} catch (NoSuchAlgorithmException e) {
			return null;
		}

	}


	/**
	 * 将Map转换为排序后的字符串
	 *
	 * @param map 输入的Map
	 * @return 排序后的字符串
	 */
	private static String mapToSortedString(Map<String, Object> map) {
		return map.keySet().stream()
				.sorted()
				.map(key -> key + ":" + Objects.toString(map.get(key), ""))
				.collect(Collectors.joining(","));
	}

	/**
	 * 将Map转换为排序后的字符串，排除时间相关字段
	 *
	 * @param map 输入的Map
	 * @return 排序后的字符串
	 */
	private static String mapToSortedStringExcludeTimeFields(Map<String, Object> map) {
		return map.keySet().stream()
				.filter(key -> !isTimeField(key)) // 过滤掉时间字段
				.sorted()
				.map(key -> key + ":" + Objects.toString(map.get(key), ""))
				.collect(Collectors.joining(","));
	}

	/**
	 * 判断是否为时间字段
	 *
	 * @param fieldName 字段名
	 * @return 是否为时间字段
	 */
	private static boolean isTimeField(String fieldName) {
		if (fieldName == null) {
			return false;
		}
		String lowerFieldName = fieldName.toLowerCase();
		return lowerFieldName.equals("update_time")
			|| lowerFieldName.equals("create_time")
			|| lowerFieldName.equals("updatetime")
			|| lowerFieldName.equals("createtime")
			|| lowerFieldName.contains("_time")
			|| lowerFieldName.contains("time_")
			|| lowerFieldName.endsWith("time");
	}


	/**
	 * 将字节数组转换为十六进制字符串
	 *
	 * @param bytes 字节数组
	 * @return 十六进制字符串
	 */
	private static String bytesToHex(byte[] bytes) {
		StringBuilder hex = new StringBuilder();
		for (byte b : bytes) {
			hex.append(String.format("%02x", b));
		}
		return hex.toString();
	}


}
