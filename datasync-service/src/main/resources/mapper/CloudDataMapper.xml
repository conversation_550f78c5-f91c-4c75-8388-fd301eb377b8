<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tjsj.sync.modules.sync.mapper.CloudDataMapper">
    <select id="test" resultType="java.util.Map">
        select *
        from tj_middle_ground.t_stock_info
        where stock_id = '000001'
    </select>

    <select id="selectTableDataByDataRange" resultType="java.util.Map">
        SELECT *
        FROM ${tableConfig.dataSchemaName}.${tableConfig.dataTableName}
        <choose>
            <when test="syncType != null and syncType.code == 2">
                <if test="null != updateTime">
                    WHERE update_time >= #{updateTime,jdbcType=TIMESTAMP}
                </if>
                <if test="syncType.code == 2">
                    order by update_time asc
                </if>
            </when>
            <otherwise>
                <if test="null != updateTime">
                    WHERE update_time >= #{updateTime,jdbcType=TIMESTAMP}
                </if>
            </otherwise>
        </choose>

        limit ${offset}, ${batchSize}
    </select>

    <select id="selectTableDataByDataRangeTest" resultType="java.util.Map">
        SELECT *
        FROM ${table.schemaName}.${table.tableName}

        limit ${offset}, ${batchSize}
    </select>

    <select id="selectTableDataById" resultType="java.util.Map">
        SELECT *
        FROM ${table.schemaName}.${table.tableName}
        WHERE id > #{id}
    </select>


    <select id="selectTableDataByCondition" resultType="java.util.Map">
        select *
        from ${syncInfo.insertTableName}
        <where>
            <if test="syncInfo.insertTableCondition != null and syncInfo.insertTableCondition != ''">
                ${syncInfo.insertTableCondition}
            </if>
            <if test="syncInfo.insertStartId != null and syncInfo.insertStartId != ''">
                and id >= #{syncInfo.insertStartId}
            </if>
            <if test="syncInfo.insertStartUpdateTime != null and syncInfo.insertStartUpdateTime != ''">
                and update_time >= #{syncInfo.insertStartUpdateTime}
            </if>
        </where>
        limit ${offset}, ${batchSize}
    </select>

    <select id="executeSqlToCloud">
        ${executeSql}
    </select>

    <insert id="batchInsertTableDataByCondition">
        INSERT INTO ${syncInfo.deleteTableName}
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <foreach collection="dataList[0].keySet()" item="key">
                ${key},
            </foreach>
        </trim>
        VALUES
        <foreach collection="dataList" item="data" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <foreach collection="data" item="value" index="key">
                    #{value},
                </foreach>
            </trim>
        </foreach>
        ON DUPLICATE KEY UPDATE
        <foreach collection="dataList[0].keySet()" item="key" separator=",">
            ${key} =
            VALUES (${key})
        </foreach>
    </insert>

    <select id="truncateTableData">
        truncate table ${deleteTableName}
    </select>

    <delete id="deleteTableDataByCondition">
        delete
        from ${deleteTableName}
        <where>
            <if test="deleteTableCondition != null and deleteTableCondition != ''">
                ${deleteTableCondition}
            </if>
            <if test="deleteStartId">
                and id >= #{deleteStartId,jdbcType=INTEGER}
            </if>
            <if test="deleteStartUpdateTime != null">
                and update_time >= #{deleteStartUpdateTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </delete>

    <select id="listTableColumns" resultType="java.lang.String">
        SELECT COLUMN_NAME
        from information_schema.`COLUMNS`
        WHERE TABLE_SCHEMA = #{schemaName,jdbcType=VARCHAR}
          and TABLE_NAME = #{tableName,jdbcType=VARCHAR}
    </select>

    <delete id="truncateInsertTableData">
        truncate table ${tableConfig.schemaName}.${tableConfig.tableName}
    </delete>

    <select id="getTableMaxUpdateTime" resultType="java.time.LocalDateTime">
        select max(update_time) as update_time
        from ${tableConfig.schemaName}.${tableConfig.tableName}
    </select>

    <insert id="batchInsertTableData">
        INSERT INTO ${tableConfig.schemaName}.${tableConfig.tableName}
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <foreach collection="dataList[0].keySet()" item="key">
                ${key},
            </foreach>
        </trim>
        VALUES
        <foreach collection="dataList" item="data" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <foreach collection="data" item="value" index="key">
                    #{value},
                </foreach>
            </trim>
        </foreach>
        ON DUPLICATE KEY UPDATE
        <foreach collection="dataList[0].keySet()" item="key" separator=",">
            ${key} = VALUES(${key})
        </foreach>
    </insert>

    <select id="getTableGroupByDataCount" resultType="TypeCountVO">

        select f.${tableGroupByColumn} as typeName, ifnull(count(0),0) as count
            from ${dataSchemaName}.${dataTableName} f
        group by f.${tableGroupByColumn} ;

    </select>

    <select id="selectFinancialTableDataByOffset" resultType="java.util.Map">
        select *
        from ${dataSchemaName}.${dataTableName} f
        where f.${tableGroupByColumnList[0]} = #{groupByTierOneColumnValue,jdbcType=VARCHAR}
        <if test="tableGroupByColumnList.size() > 1 and groupByTierTwoColumnValue != null and groupByTierTwoColumnValue != '' ">
            and f.${tableGroupByColumnList[1]} = #{groupByTierTwoColumnValue,jdbcType=VARCHAR}
        </if>
        limit #{offset,jdbcType=INTEGER}, #{pullBatchSize,jdbcType=INTEGER};

    </select>

    <select id="getTableGroupByDataCountTierTwo" resultType="TypeCountVO">
        select f.${tableGroupByColumnTierTwo} as typeName, ifnull(count(0),0) as count
        from ${dataSchemaName}.${dataTableName} f
        where f.${tableGroupByColumnTierOne} = #{filteredGroupByColumnValue,jdbcType=VARCHAR}
        group by f.${tableGroupByColumnTierTwo}
        order by f.${tableGroupByColumnTierTwo};
    </select>

    <select id="getFieldNameValueList" resultType="java.lang.String">
        SELECT
            f.FiledName
        FROM
            ${dataSchemaName}.${dataTableName} f
        GROUP BY
            f.FiledName;
    </select>

    <delete id="testDeleteDistributedLock">
        delete
        from tj_middle_ground.distributed_lock
        where id = 1;
    </delete>
</mapper>