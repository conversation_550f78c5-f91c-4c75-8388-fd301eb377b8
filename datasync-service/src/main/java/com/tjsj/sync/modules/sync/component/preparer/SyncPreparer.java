package com.tjsj.sync.modules.sync.component.preparer;


import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.tjsj.common.config.TarkinConfig;
import com.tjsj.common.enums.EnvironmentTypeEnum;
import com.tjsj.common.enums.base.CommonStatus;
import com.tjsj.common.enums.sync.SyncTypeEnum;
import com.tjsj.common.utils.date.DateUtils;
import com.tjsj.modules.log.model.entity.DatabaseSyncHistoryDO;
import com.tjsj.modules.log.service.DatabaseSyncHistoryService;
import com.tjsj.modules.sync.model.entity.DataSyncManualDO;
import com.tjsj.modules.sync.model.entity.SyncTableConfigDO;
import com.tjsj.modules.sync.service.SyncTableConfigService;
import com.tjsj.sync.modules.sync.model.SyncContext;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * SyncPreparer
 *
 * <AUTHOR> Ye
 * @date 2025/08/01
 * @version 1.0.0
 * @description 同步准备器
 */
@Component
@RequiredArgsConstructor
public class SyncPreparer {

    private final SyncTableConfigService syncTableConfigService;

    private final TarkinConfig tarkinConfig;


    public void prepare(SyncContext context) {
        updateSyncStatus(context);
        createSyncHistory(context);
        context.setStartTime(System.currentTimeMillis());
    }

    /**
     * 更新同步状态
     *
     * @description 将同步配置表的同步状态设置为正在同步、上次同步开始时间设置为当前时间
     * @param context 上下文
     *
     * <AUTHOR> Ye
     * @date 2025/08/01
     */
    public void updateSyncStatus(SyncContext context) {
        syncTableConfigService.update(Wrappers.<SyncTableConfigDO>lambdaUpdate()
                .set(SyncTableConfigDO::getIfSyncing, CommonStatus.ENABLE)
                .set(SyncTableConfigDO::getLastSyncStartTime, LocalDateTime.now())
                .eq(SyncTableConfigDO::getId, context.getSyncTableConfig().getId()));
    }

    /**
     * 创建同步历史记录
     *
     * @description 创建同步历史记录，并将同步历史记录ID设置到上下文中
     * @param context 上下文
     *
     * <AUTHOR> Ye
     * @date 2025/08/01
     */
    public void createSyncHistory(SyncContext context) {
        DatabaseSyncHistoryDO syncHistory = createSyncHistoryRecord(
                context.getTaskId(), null, context.getSyncTableConfig(), context.getSyncTypeEnum());
        context.setSyncHistory(syncHistory);
    }

    /**
     * 创建同步历史记录
     *
     * @description 创建同步历史记录
     * @param taskId 任务ID
     * @param syncManual 手动同步信息
     * @param tableConfig 同步配置信息
     * @param syncTypeEnum 同步类型
     * @return 同步历史记录
     *
     * <AUTHOR> Ye
     * @date 2025/08/01
     */
    public DatabaseSyncHistoryDO createSyncHistoryRecord(Integer taskId, DataSyncManualDO syncManual,
                                                         SyncTableConfigDO tableConfig, SyncTypeEnum syncTypeEnum) {

        DatabaseSyncHistoryDO databaseSyncHistory = new DatabaseSyncHistoryDO()
                .setDate(DateUtils.format(new Date(), DateUtils.DATE_PATTERN))
                .setStartTime(LocalDateTime.now())
                .setProjectId(tableConfig.getProjectId())
                .setDbType(EnumUtil.getBy(EnvironmentTypeEnum::getCode, tarkinConfig.getDbType()))
                .setProfileType(tarkinConfig.getProfile())
                .setSyncType(syncTypeEnum != null ?
                        syncTypeEnum : (syncManual != null ? SyncTypeEnum.MANUAL : SyncTypeEnum.AUTO))
                .setTaskType(syncManual != null ? syncManual.getTaskType() : tableConfig.getTaskType())
                .setTaskId(taskId);

        if (syncManual != null) {
            // 设置插入表名
            String insertTableName = syncManual.getInsertTableName();
            if (StrUtil.isNotEmpty(insertTableName)) {
                // 取出数据库名和表名
                String[] split = insertTableName.split("\\.");
                String databaseName = split[0];
                String tableName = split[1];
                databaseSyncHistory.setDataSchemaName(databaseName)
                        .setDataTableName(tableName);
                if (StrUtil.isNotEmpty(syncManual.getDeleteTableName())) {
                    String[] deleteSplit = syncManual.getDeleteTableName().split("\\.");
                    String deleteDatabaseName = deleteSplit[0];
                    String deleteTableName = deleteSplit[1];
                    databaseSyncHistory.setSchemaName(deleteDatabaseName)
                            .setTableName(deleteTableName);
                }
            } else if (StrUtil.isNotEmpty(syncManual.getExecuteSql())) {
                // 将执行语句设置到同步历史日志中
                databaseSyncHistory.setExecuteSql(syncManual.getExecuteSql());
            }
        } else {
            databaseSyncHistory.setSchemaName(tableConfig.getSchemaName())
                    .setTableName(tableConfig.getTableName())
                    .setDataSchemaName(tableConfig.getDataSchemaName())
                    .setDataTableName(tableConfig.getDataTableName());
        }


        return databaseSyncHistory;
    }

}
