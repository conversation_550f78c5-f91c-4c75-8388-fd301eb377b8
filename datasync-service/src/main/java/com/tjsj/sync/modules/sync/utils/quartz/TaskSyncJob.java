package com.tjsj.sync.modules.sync.utils.quartz;

import com.tjsj.common.annotation.SchedulerSwitchControl;
import com.tjsj.common.annotation.ServiceSwitchControl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * TaskSyncJob
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/12/17 12:49
 * @description 任务同步job
 */
// 生效条件：配置文件中quartz.enabled=true
@ConditionalOnProperty(name = "quartz.enabled", havingValue = "true", matchIfMissing = true)
@Component
@Slf4j
public class TaskSyncJob {

    @Resource
    private DynamicTaskScheduler dynamicTaskScheduler;

    /**
     * 管理Quartz定时任务
     *
     * <AUTHOR>
     * @date 2024/12/17
     */
    // Quartz定时任务执行频率
    @Scheduled(cron = "${tarkin.scheduler.cron.manage-quartz-job:*/5 * * * * ?}")
    @ServiceSwitchControl
    @SchedulerSwitchControl(value = "管理Quartz定时任务", scheduled = true)
    public void manageQuartzJob() {


        try {
            dynamicTaskScheduler.manageQuartzJob();

        } catch (Exception e) {
            log.error("quartz同步任务失败", e);

        }

    }

}
