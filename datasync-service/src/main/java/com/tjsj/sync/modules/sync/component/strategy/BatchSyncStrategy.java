package com.tjsj.sync.modules.sync.component.strategy;


import com.tjsj.sync.modules.sync.model.bo.SyncBatchConfig;
import com.tjsj.sync.modules.sync.model.bo.SyncContext;
import com.tjsj.sync.modules.sync.model.bo.SyncPrepareResult;
import com.tjsj.sync.modules.sync.model.bo.SyncResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * BatchSyncStrategy
 *
 * <AUTHOR>
 * @date 2025/08/01
 * @version 1.0.0
 * @description 批量同步策略
 */
@Component
@Slf4j
public class BatchSyncStrategy {

    private final DataExtractor dataExtractor;

    /**
     * 执行同步数据
     *
     * @param prepareResult 准备同步结果
     * @param context 同步上下文
     * @return 同步结果
     * <AUTHOR> Ye
     * @date 2025/08/01
     */
    public SyncResult executeSyncData(SyncPrepareResult prepareResult, SyncContext context) {
        log.debug("📦 执行批量同步策略 - 表: {}", context.getFullTableName());

        SyncBatchConfig batchConfig = prepareResult.getBatchConfig();
        LocalDateTime targetMaxTime = prepareResult.getTargetMaxTime();

        int totalDataNum = 0;
        int batchCount = 0;
        int offset = 0;

        List<Map<String, Object>> dataList;
        do {
            // 1. 数据提取
            dataList = dataExtractor.extractData(context, targetMaxTime, offset, batchConfig.getReadSize());

            log.debug("📥 本批次提取数据: {} 条 - 表: {}, 偏移: {}",
                    dataList.size(), context.getTableName(), offset);

            // 2. 数据处理流水线
            dataList = dataPipeline.process(context, dataList);

            // 3. 哈希检查
            if (!hashChecker.checkDataChange(context, dataList)) {
                if (dataList.size() != batchConfig.getReadSize()) {
                    log.debug("🔍 数据哈希值未变化且已到最后一批，结束同步 - 表: {}", context.getTableName());
                    break;
                }
            }

            // 4. 数据插入
            if (!dataList.isEmpty()) {
                int insertedCount = dataInserter.insertData(context, dataList, batchConfig);
                totalDataNum += insertedCount;
                batchCount++;

                log.debug("📤 本批次插入数据: {} 条 - 表: {}", insertedCount, context.getTableName());
            }

            offset += batchConfig.getReadSize();

        } while (dataList.size() == batchConfig.getReadSize());

        return SyncResult.builder()
                .totalDataNum(totalDataNum)
                .batchCount(batchCount)
                .success(true)
                .build();


    }


}
