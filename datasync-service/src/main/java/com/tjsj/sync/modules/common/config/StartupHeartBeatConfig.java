package com.tjsj.sync.modules.common.config;

import com.tjsj.modules.manage.service.HeartBeatTestService;
import com.tjsj.sync.modules.sync.service.DataSyncService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * StartupHeartBeatConfig
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/07/02
 * @description 启动时心跳测试配置类 - 在项目启动完成后执行一次心跳测试
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class StartupHeartBeatConfig {

    private final HeartBeatTestService heartBeatTestService;
    private final DataSyncService dataSyncService;

    /**
     * 项目启动完成后执行心跳测试
     * 使用ApplicationReadyEvent确保在所有Bean初始化完成后执行
     * 不受原方法注解的影响，直接调用底层服务
     *
     * @param event 应用就绪事件
     */
    @EventListener(ApplicationReadyEvent.class)
    public void executeStartupHeartBeat(ApplicationReadyEvent event) {
        log.info("=== 项目启动完成，开始执行心跳测试 ===");
        
        try {
            // 执行与 testCloudDatabaseHeartBeat() 方法相同的逻辑
            // 但不受其注解限制
            testCloudDatabaseHeartBeatOnStartup();
            
            log.info("=== 启动时心跳测试执行完成 ===");
        } catch (Exception e) {
            log.error("启动时心跳测试执行失败", e);
        }
    }

    /**
     * 启动时心跳测试方法
     * 复制 DatabaseSyncController.testCloudDatabaseHeartBeat() 的逻辑
     * 但不受注解控制
     */
    private void testCloudDatabaseHeartBeatOnStartup() {
        log.info("开始执行云数据库心跳测试...");
        
        // 云数据库心跳测试
        if (!heartBeatTestService.testHeartBeat()) {
            log.warn("云数据库心跳测试失败，开始执行手动同步...");
            dataSyncService.syncManual();
            heartBeatTestService.updateHeartBeatStatus();
            log.info("手动同步完成，心跳状态已更新");
        } else {
            log.info("云数据库心跳测试成功");
        }

        // 注意：原方法中的本地数据库心跳测试部分已被注释，这里保持一致
        // 如果需要启用本地数据库心跳测试，可以取消下面的注释
        /*
        //本地数据库心跳测试
        if (!localHeartBeatTestService.testHeartBeat()) {
            dataSyncService.syncManual();
            localHeartBeatTestService.updateHeartBeatStatus();
        }
        */
    }
}
