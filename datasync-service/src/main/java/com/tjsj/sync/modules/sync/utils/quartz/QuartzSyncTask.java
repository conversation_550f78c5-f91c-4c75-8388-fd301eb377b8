package com.tjsj.sync.modules.sync.utils.quartz;

import com.tjsj.common.annotation.ServiceSwitchControl;
import com.tjsj.common.constants.cache.RedisConsts;
import com.tjsj.common.enums.sync.SyncTypeEnum;
import com.tjsj.common.lock.DistributedLockHelper;
import com.tjsj.common.lock.LockUtil;
import com.tjsj.modules.sync.model.entity.SyncTableConfigDO;
import com.tjsj.sync.modules.sync.service.DataSyncService;
import com.tjsj.sync.modules.sync.utils.SyncUtil;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * QuartzSyncTask
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/12/17 10:20
 * @description Quartz 定时任务同步, 继承 Job 接口, 实现 execute 方法,用于执行定时任务同步
 */
@Slf4j
@Component
public class QuartzSyncTask implements Job {
    @Resource
    private LockUtil lockUtil;

    @Resource
    private DataSyncService dataSyncService;

    @Resource
    private DistributedLockHelper distributedLockHelper;

    @Override
    @ServiceSwitchControl
    public void execute(JobExecutionContext jobExecutionContext) {


        // 获取定时任务信息Map
        JobDataMap jobDataMap = jobExecutionContext.getJobDetail().getJobDataMap();
        String lockKey = lockUtil.generateLockKey(
                RedisConsts.KEY_LOCK + SyncTypeEnum.QUARTZ + ":" + jobDataMap.get(SyncTableConfigDO.Fields.id));

        distributedLockHelper.executeWithLock(
                lockKey,
                // 锁超时时间
                null,
                //租约十分钟
                10L,
                () -> {

                    // 真正需要执行的逻辑
                    // 封装 SyncTableConfigDO 对象
                    SyncTableConfigDO syncTableConfigDO = SyncUtil.buildSyncTableConfigFromJobDataMap(jobDataMap);

                    log.info("{}.{} 开始进行Quartz定时任务同步 {}",
                            syncTableConfigDO.getSchemaName(),
                            syncTableConfigDO.getTableName(),
                            LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

                    // 执行同步逻辑
                    dataSyncService.syncSingleTableData(null, syncTableConfigDO, true, SyncTypeEnum.QUARTZ);
                    return null;
                }
        );


    }


}