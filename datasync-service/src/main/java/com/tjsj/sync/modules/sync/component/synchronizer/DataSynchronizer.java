package com.tjsj.sync.modules.sync.component.synchronizer;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import com.tjsj.common.enums.TaskExecuteStatusEnum;
import com.tjsj.common.enums.base.CommonStatus;
import com.tjsj.common.enums.sync.SyncTaskTypeEnum;
import com.tjsj.common.enums.sync.SyncTypeEnum;
import com.tjsj.common.utils.data.HashUtil;
import com.tjsj.modules.log.model.entity.DatabaseSyncHistoryDO;
import com.tjsj.sync.modules.sync.mapper.CloudDataMapper;
import com.tjsj.sync.modules.sync.mapper.LocalDataMapper;
import com.tjsj.sync.modules.sync.model.bo.SyncContext;
import com.tjsj.sync.modules.sync.model.entity.SyncTableConfigDO;
import com.tjsj.sync.modules.sync.service.SyncTableConfigService;
import com.tjsj.sync.modules.sync.utils.MySqlKeyword;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * DataSynchronizer
 *
 * <AUTHOR> Ye
 * @date 2025/08/01
 * @version 1.0.0
 * @description 数据同步器
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class DataSynchronizer {

    @Resource
    private LocalDataMapper localDataMapper;

    @Resource
    private CloudDataMapper cloudDataMapper;

    @Resource
    private SyncTableConfigService syncTableConfigService;


    /**
     * 同步表数据 - 重构版本
     *
     * <p>该方法是从原始的 batchInsertTableData 方法重构而来，
     * 保持完全相同的功能和行为，但与 SyncContext 集成。</p>
     *
     * @param context 同步上下文
     * @throws RuntimeException 如果同步过程中发生错误
     */
    public void synchronizeTableData(SyncContext context) {

        SyncTableConfigDO syncTableConfig = context.getSyncTableConfig();
        DatabaseSyncHistoryDO syncHistoryRecord = context.getSyncHistory();
        Boolean ifInsert = context.getIfInsert();
        SyncTypeEnum syncTypeEnum = context.getSyncTypeEnum();

        int thisTableSyncTotalDataNum = 0;

        log.debug("🔄 开始同步表数据 - 表: {}", context.getTableName());

        // Step 1: 如果是全量更新，清空目标表数据
        if (this.isFullUpdate(syncTableConfig, ifInsert)) {
            log.debug("🗑️ 执行全量更新，清空目标表 - 表: {}", context.getTableName());
            this.truncateTableData(syncTableConfig);
        }

        // Step 2: 获取目标表最大更新时间
        LocalDateTime targetTableMaxUpdateTime = this.getTableMaxUpdateTime(syncTableConfig);
        log.debug("⏰ 目标表最大更新时间: {} - 表: {}", targetTableMaxUpdateTime, context.getTableName());

        // Step 3: 初始化循环参数
        int offset = 0;
        int batchReadSize = resolveBatchReadSize(syncTableConfig);
        List<Map<String, Object>> insertDataList;

        log.debug("📊 批量读取大小: {} - 表: {}", batchReadSize, context.getTableName());

        // Step 4: 循环处理数据
        do {
            // 获取数据并进行字段过滤
            insertDataList = this.selectAndFilterData(syncTableConfig, targetTableMaxUpdateTime, offset, batchReadSize,
                    syncHistoryRecord);

            log.debug("📥 本批次提取数据: {} 条 - 表: {}, 偏移: {}",
                    insertDataList.size(), context.getTableName(), offset);

            // 检查数据哈希值
            Boolean ifNewDataHash = this.checkDataHashValue(insertDataList, syncTableConfig, syncTypeEnum);

            // 如果数据未变化且已到最后一批，跳出循环
            if (!ifNewDataHash && insertDataList.size() != batchReadSize) {
                log.debug("🔍 数据哈希值未变化且已到最后一批，结束同步 - 表: {}", context.getTableName());
                break;
            }

            // 执行批量插入并累加统计量
            if (!insertDataList.isEmpty()) {
                int batchInsertCount = batchInsertData(syncTableConfig, syncHistoryRecord, insertDataList, ifInsert);
                thisTableSyncTotalDataNum += batchInsertCount;

                log.debug("📤 本批次插入数据: {} 条 - 表: {}", batchInsertCount, context.getTableName());
            }

            offset += batchReadSize;
        } while (insertDataList.size() == batchReadSize);

        // Step 5: 更新上下文中的总数据量
        context.setTotalDataNum(thisTableSyncTotalDataNum);

        log.debug("✅ 表数据同步完成 - 表: {}, 总数据量: {} 条",
                context.getTableName(), thisTableSyncTotalDataNum);

    }


    /**
     * 判断是否全量更新
     *
     * @param tableConfig 表配置
     * @param ifInsert    如果插入
     * @return boolean
     * <AUTHOR> Ye
     * @date 2024/11/19
     */
    private boolean isFullUpdate(SyncTableConfigDO tableConfig, Boolean ifInsert) {
        return tableConfig.getIfFullUpdate().equals(CommonStatus.ENABLE) && ifInsert;
    }


    /**
     * 截断表数据
     *
     * @param tableConfig 表配置
     * <AUTHOR> Ye
     * @date 2024/10/16
     */
    private void truncateTableData(@NotNull SyncTableConfigDO tableConfig) {
        SyncTaskTypeEnum taskType = tableConfig.getTaskType();

        if (taskType.equals(SyncTaskTypeEnum.CLOUD_TO_LOCAL)) {
            localDataMapper.truncateInsertTableData(tableConfig);
        } else if (taskType.equals(SyncTaskTypeEnum.LOCAL_TO_CLOUD)) {
            cloudDataMapper.truncateInsertTableData(tableConfig);
        }

    }


    /**
     * 获取表最大值更新时间
     *
     * @param tableConfig 表配置
     * @return {@link LocalDateTime }
     * <AUTHOR> Ye
     * @date 2024/10/16
     */
    private LocalDateTime getTableMaxUpdateTime(SyncTableConfigDO tableConfig) {

        SyncTaskTypeEnum taskType = tableConfig.getTaskType();
        if (taskType.equals(SyncTaskTypeEnum.CLOUD_TO_LOCAL)) {

            return localDataMapper.getTableMaxUpdateTime(tableConfig);
        } else if (taskType.equals(SyncTaskTypeEnum.LOCAL_TO_CLOUD)) {
            return cloudDataMapper.getTableMaxUpdateTime(tableConfig);
        }
        return null;
    }


    /**
     * 自动拉批量大小
     */
    @Value("${sync.auto.pull-batch-size:10000}")
    private int autoPullBatchSize;

    /**
     * 解析读取批量大小
     *
     * @param tableConfig 表配置
     * @return int
     * <AUTHOR> Ye
     * @date 2024/11/19
     */
    private int resolveBatchReadSize(SyncTableConfigDO tableConfig) {
        Integer configSize = tableConfig.getReadSize();
        return configSize != null ? configSize : autoPullBatchSize;
    }


    /**
     * 选择和过滤数据
     *
     * @param tableConfig       表配置
     * @param updateTime        更新时间
     * @param offset            抵消
     * @param batchReadSize     批量阅读大小
     * @param syncHistoryRecord 同步历史记录
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR> Ye
     * @date 2024/11/19
     */
    private @NotNull List<Map<String, Object>> selectAndFilterData(SyncTableConfigDO tableConfig,
                                                                   LocalDateTime updateTime, int offset,
                                                                   int batchReadSize,
                                                                   DatabaseSyncHistoryDO syncHistoryRecord) {

        SyncTypeEnum syncType = syncHistoryRecord.getSyncType();

        // Step 1: 从数据源提取数据
        List<Map<String, Object>> dataList = this.selectTableDataByDataRange(tableConfig, updateTime, offset,
                batchReadSize, syncType);

        // 过滤要同步的字段列
        this.filterTableColumns(tableConfig, dataList, syncHistoryRecord);

        // Step 2: 筛选字段并处理关键字
        List<Map<String, Object>> filteredList = new ArrayList<>();
        this.filterDataColumns(tableConfig, dataList);
        for (Map<String, Object> record : dataList) {
            filteredList.add(MySqlKeyword.processKeywords(record));
        }


        return filteredList;
    }


    /**
     * 选择表数据通过数据范围
     *
     * @param tableConfig   表配置
     * @param updateTime    更新时间
     * @param offset        抵消
     * @param batchReadSize 批量阅读大小
     * @param syncType      同步类型
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR> Ye
     * @date 2024/10/16
     */
    private List<Map<String, Object>> selectTableDataByDataRange(SyncTableConfigDO tableConfig,
                                                                 LocalDateTime updateTime, int offset,
                                                                 int batchReadSize, SyncTypeEnum syncType) {
        SyncTaskTypeEnum taskType = tableConfig.getTaskType();

        if (taskType.equals(SyncTaskTypeEnum.CLOUD_TO_LOCAL)) {
            // #1) 云库到本地库
            return cloudDataMapper.selectTableDataByDataRange(tableConfig, updateTime, offset, batchReadSize,
                    syncType);
        } else if (taskType.equals(SyncTaskTypeEnum.LOCAL_TO_CLOUD)) {
            // #2) 本地库到云库
            return localDataMapper.selectTableDataByDataRange(tableConfig, updateTime, offset, batchReadSize,
                    syncType);
        }

        return new ArrayList<>();
    }

    /**
     * 过滤表中的列
     * <p>过滤调用数据源表中存在，但是目标表不存在的字段</p>
     *
     * @param tableConfig       表配置
     * @param dataList          数据列表
     * @param syncHistoryRecord 同步历史记录
     * <AUTHOR> Ye
     * @date 2024/12/17
     */
    public void filterTableColumns(@NotNull SyncTableConfigDO tableConfig, List<Map<String, Object>> dataList,
                                   DatabaseSyncHistoryDO syncHistoryRecord) {

        // 将所有表字段转换为小写
        String dataSchemaName = tableConfig.getDataSchemaName();
        String dataTableName = tableConfig.getDataTableName();
        Set<String> cloudTableColumns = new HashSet<>(cloudDataMapper.listTableColumns(dataSchemaName, dataTableName));
        cloudTableColumns = cloudTableColumns.stream().map(String::toLowerCase).collect(Collectors.toSet());

        String schemaName = tableConfig.getSchemaName();
        String tableName = tableConfig.getTableName();
        Set<String> localTableColumns = new HashSet<>(localDataMapper.listTableColumns(schemaName, tableName));
        localTableColumns = localTableColumns.stream().map(String::toLowerCase).collect(Collectors.toSet());

        // 计算出数据源表存在，但是目标表不存在的字段
        cloudTableColumns.removeAll(localTableColumns);
        if (CollUtil.isEmpty(cloudTableColumns)) {
            return;
        }

        // 将字段列表拼接成逗号分隔的字符串
        String columnsString = CollUtil.join(cloudTableColumns, ",");
        // 拼接最终 remark 字符串
        String remark = StrUtil.format("数据源表存在，但是目标表不存在的字段：{}", columnsString);
        if (syncHistoryRecord != null) {
            syncHistoryRecord.setRemark(remark);
        }

        // 过滤 dataList，将不存在于目标表的字段删除
        Set<String> finalCloudTableColumns = cloudTableColumns;
        dataList.forEach(data -> finalCloudTableColumns.forEach(data::remove));

    }

    /**
     * 过滤数据列
     *
     * @param tableConfig    同步表配置
     * @param insertDataList 需要插入的数据列表
     * <AUTHOR> Ye
     * @date 2024/10/16
     */
    public void filterDataColumns(@NotNull SyncTableConfigDO
                                          tableConfig, List<Map<String, Object>> insertDataList) {

        // TODO: 250724 处理数据的createTime或者updateTime字段值为0000-00-00 00:00:00的情况，不过暂时不用处理

        String includeColumn = tableConfig.getIncludeColumn();
        String excludeColumn = tableConfig.getExcludeColumn();

        // 保留指定列
        if (StrUtil.isNotEmpty(includeColumn)) {
            List<String> includeColumnList = Arrays.stream(includeColumn.split(",")).toList();
            insertDataList.forEach(data -> {
                data.keySet().retainAll(includeColumnList);
            });
        }

        // 排除指定列
        if (StrUtil.isNotEmpty(excludeColumn)) {
            List<String> excludeColumnList = Arrays.stream(excludeColumn.split(",")).toList();
            insertDataList.forEach(data -> {
                excludeColumnList.forEach(data::remove);
            });
        }

    }


    /**
     * 比较数据hash值
     *
     * @param insertDataList  插入数据列表
     * @param syncTableConfig 同步表配置
     * @param syncTypeEnum    同步类型
     * @return {@link Boolean }
     * <AUTHOR> Ye
     * @date 2025/05/21
     */
    public Boolean checkDataHashValue(List<Map<String, Object>> insertDataList, SyncTableConfigDO syncTableConfig,
                                      SyncTypeEnum syncTypeEnum) {

        // 如果是quartz同步，则不比较hash值，直接返回true
        if (!Objects.equals(syncTypeEnum, SyncTypeEnum.QUARTZ)) {
            return true;
        }

        Optional<String> optionalHash =
                Optional.ofNullable(syncTableConfigService.getById(syncTableConfig.getId()))
                        .map(SyncTableConfigDO::getQuartzLastSyncDataHash);

        String oldDataHashValue = optionalHash.orElse(null);
        // 生成新的数据哈希值
        String newDataHashValue = HashUtil.generateHash(insertDataList);

        syncTableConfig.setQuartzLastSyncDataHash(newDataHashValue);

        // 如果新数据的哈希值与旧数据的哈希值相同，则不进行同步
        return !Objects.equals(oldDataHashValue, newDataHashValue);
    }


    /**
     * 批量插入数据
     * <p>对插入数据列表进行分批次插入，并记录每批次插入日志</p>
     *
     * @param syncTableConfig   同步表配置
     * @param syncHistoryRecord 同步历史记录
     * @param insertDataList    插入数据列表
     * @param ifInsert          是否插入
     * @return int
     * <AUTHOR> Ye
     * @date 2024/11/19
     */
    private int batchInsertData(SyncTableConfigDO syncTableConfig, DatabaseSyncHistoryDO syncHistoryRecord,
                                List<Map<String, Object>> insertDataList, Boolean ifInsert) {
        if (!ifInsert) {
            return 0;
        }

        // 计算批次大小
        int thisTableSyncTotalDataNum = 0;
        int batchInsertSize = resolveBatchInsertSize(syncTableConfig);

        for (int thisBatchInsertStartIndex = 0; thisBatchInsertStartIndex < insertDataList.size();
             thisBatchInsertStartIndex += batchInsertSize) {

            int thisBatchInsertEndIndex = Math.min(thisBatchInsertStartIndex + batchInsertSize, insertDataList.size());
            List<Map<String, Object>> insertDataSubList =
                    new ArrayList<>(insertDataList.subList(thisBatchInsertStartIndex, thisBatchInsertEndIndex));

            // 插入数据并记录日志
            if (CollUtil.isNotEmpty(insertDataSubList)) {

                Boolean ifInsertSuccess = retryBatchInsert(syncTableConfig, syncHistoryRecord, insertDataSubList);
                if (!ifInsertSuccess) {
                    break;
                }
                thisTableSyncTotalDataNum += insertDataSubList.size();
            }
        }

        return thisTableSyncTotalDataNum;
    }

    /**
     * 自动插入批量大小
     */
    @Value("${sync.auto.insert-batch-size:10000}")
    private int autoInsertBatchSize;


    /**
     * 解析插入批量大小
     *
     * @param tableConfig 表配置
     * @return int
     * <AUTHOR> Ye
     * @date 2024/11/19
     */
    private int resolveBatchInsertSize(SyncTableConfigDO tableConfig) {
        Integer configSize = tableConfig.getBatchSize();
        return configSize != null ? configSize : autoInsertBatchSize;
    }

    /**
     * 重试延迟毫秒
     */
    @Value("${sync.auto.retry-delay-millis:2000}")
    private long retryDelayMillis;

    /**
     * 最大重试次数
     */
    @Value("${sync.auto.max-retry-times:2}")
    private int maxRetryTimes;

    /**
     * 重试批量插入
     * <p>对当前批次的数据子列表进行批量插入，如果失败，则进行重试，直到成功或者达到最大重试次数</p>
     * <p>将重试次数记录在同步历史记录中</p>
     *
     * @param syncTableConfig   表配置
     * @param syncHistoryRecord 同步历史
     * @param subInsertDataList 子列表
     * @return {@link Boolean }
     * <AUTHOR> Ye
     * @date 2024/11/19
     */
    @SuppressWarnings("ConstantConditions")
    private Boolean retryBatchInsert(SyncTableConfigDO syncTableConfig, DatabaseSyncHistoryDO syncHistoryRecord,
                                     List<Map<String, Object>> subInsertDataList) {
        int retryCount = 0;

        while (retryCount < maxRetryTimes) {
            try {
                // 根据任务类型插入数据
                SyncTaskTypeEnum taskType = syncTableConfig.getTaskType();

                if (taskType.equals(SyncTaskTypeEnum.CLOUD_TO_LOCAL)) {
                    localDataMapper.batchInsertTableData(syncTableConfig, subInsertDataList);
                } else if (taskType.equals(SyncTaskTypeEnum.LOCAL_TO_CLOUD)) {
                    cloudDataMapper.batchInsertTableData(syncTableConfig, subInsertDataList);
                }

                // 如果执行成功，返回 true
                syncHistoryRecord.setRetryCount(retryCount);
                return true;

            } catch (Exception e) {
                retryCount++;
                log.error("❌ 同步数据失败，同步表：{}，重试次数：{}，异常信息：{}", syncTableConfig.getTableName(), retryCount,
                        e.getMessage());

                if (retryCount >= maxRetryTimes) {
                    String remark = syncHistoryRecord.getRemark() == null ? "" : syncHistoryRecord.getRemark() + ";";
                    syncHistoryRecord.setTaskExecuteStatus(TaskExecuteStatusEnum.FAILURE)
                            .setRemark(remark + e.getMessage());
                    // 达到最大重试次数，返回 false
                    return false;
                }

                // 等待重试
                ThreadUtil.sleep(retryDelayMillis);

            }
        }

        // 如果重试次数未达到最大次数并且任务成功，则返回 true
        syncHistoryRecord.setRetryCount(retryCount);
        return true;
    }

}
