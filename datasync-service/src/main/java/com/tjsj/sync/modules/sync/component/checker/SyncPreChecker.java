package com.tjsj.sync.modules.sync.component.checker;


import com.tjsj.modules.sync.service.SyncTableConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * SyncPreChecker
 *
 * <AUTHOR>
 * @date 2025/08/01
 * @version 1.0.0
 * @description 同步预检查器
 * <p>负责在数据同步前进行各种必要的检查，确保同步条件满足</p>
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class SyncPreChecker {

    private final SyncTableConfigService syncTableConfigService;

}
