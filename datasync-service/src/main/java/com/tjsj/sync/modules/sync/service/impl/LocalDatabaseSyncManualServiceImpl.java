package com.tjsj.sync.modules.sync.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tjsj.sync.modules.sync.mapper.LocalDatabaseSyncManualMapper;
import com.tjsj.sync.modules.sync.model.LocalDatabaseSyncManual;
import com.tjsj.sync.modules.sync.service.LocalDatabaseSyncManualService;
import org.springframework.stereotype.Service;

/**
 * CloudDatabaseSyncManualServiceImpl
 *
 * <AUTHOR>
 * @date 2024/08/07
 * @description 针对表【t_database_sync_info(数据库同步指定表)】的数据库操作Service实现
 */
@Service
public class LocalDatabaseSyncManualServiceImpl extends ServiceImpl<LocalDatabaseSyncManualMapper,
        LocalDatabaseSyncManual>
        implements LocalDatabaseSyncManualService {

}




