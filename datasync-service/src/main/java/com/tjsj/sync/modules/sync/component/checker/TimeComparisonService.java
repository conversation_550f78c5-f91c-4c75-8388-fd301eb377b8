package com.tjsj.sync.modules.sync.component.checker;

import com.tjsj.common.enums.sync.SyncTaskTypeEnum;
import com.tjsj.modules.sync.model.entity.SyncTableConfigDO;
import com.tjsj.sync.modules.sync.mapper.CloudDataMapper;
import com.tjsj.sync.modules.sync.mapper.LocalDataMapper;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 时间比较服务
 *
 * <p>负责比较源数据库和目标数据库表的最大更新时间，判断是否需要进行数据同步。
 * 该服务是数据同步流程中的重要组件，通过时间比较避免不必要的数据同步操作，
 * 提高系统性能和减少资源消耗。</p>
 *
 * <h3>🎯 主要功能</h3>
 * <ul>
 *   <li><strong>时间获取</strong>：从云端和本地数据库获取表的最大更新时间</li>
 *   <li><strong>时间比较</strong>：比较源表和目标表的最大更新时间</li>
 *   <li><strong>同步判断</strong>：基于时间比较结果决定是否需要进行数据同步</li>
 *   <li><strong>异常处理</strong>：处理时间获取过程中的各种异常情况</li>
 * </ul>
 *
 * <h3>🔄 工作原理</h3>
 * <ol>
 *   <li>根据同步任务类型确定源表和目标表</li>
 *   <li>分别获取源表和目标表的最大更新时间</li>
 *   <li>比较两个时间，如果源表时间更新则需要同步</li>
 *   <li>处理时间为null的边界情况</li>
 * </ol>
 *
 * <h3>⚡ 性能优化</h3>
 * <ul>
 *   <li>避免不必要的数据同步操作</li>
 *   <li>减少数据库查询和网络传输</li>
 *   <li>提高整体同步效率</li>
 * </ul>
 *
 * <AUTHOR> Ye
 * @version 1.0.0
 * @since 2025-08-01
 * @see SyncTableConfigDO
 * @see SyncTaskTypeEnum
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class TimeComparisonService {

    /**
     * 云端数据映射器
     */
    private final CloudDataMapper cloudDataMapper;

    /**
     * 本地数据映射器
     */
    private final LocalDataMapper localDataMapper;

    /**
     * 时间格式化器 - 用于日志输出
     */
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 比较表最大更新时间
     *
     * <p>该方法是时间比较服务的核心方法，通过比较源数据库和目标数据库表的最大更新时间，
     * 判断是否需要进行数据同步。只有当源表的最大更新时间晚于目标表时，才需要进行同步。</p>
     *
     * <h4>🔍 比较逻辑</h4>
     * <ul>
     *   <li><strong>云到本地同步</strong>：比较云端表和本地表的最大更新时间</li>
     *   <li><strong>本地到云同步</strong>：比较本地表和云端表的最大更新时间</li>
     *   <li><strong>时间为null处理</strong>：如果任一时间为null，默认需要同步</li>
     * </ul>
     *
     * <h4>⚠️ 边界情况处理</h4>
     * <ul>
     *   <li>源表时间为null：默认需要同步（可能是新表）</li>
     *   <li>目标表时间为null：默认需要同步（可能是空表）</li>
     *   <li>两个时间都为null：默认需要同步</li>
     * </ul>
     *
     * @param syncTableConfig 同步表配置，包含表信息和任务类型
     * @return true-需要进行同步，false-不需要同步
     * @throws IllegalArgumentException 如果同步表配置为null
     * @throws RuntimeException 如果数据库查询失败
     */
    public Boolean compareTableMaxTime(SyncTableConfigDO syncTableConfig) {
        // 参数验证
        if (syncTableConfig == null) {
            log.error("❌ 同步表配置不能为null");
            throw new IllegalArgumentException("同步表配置不能为null");
        }

        String tableName = syncTableConfig.getTableName();
        SyncTaskTypeEnum syncTaskType = syncTableConfig.getTaskType();

        log.debug("🔍 开始比较表 {} 的最大更新时间，任务类型: {}", tableName, syncTaskType.getDescription());

        try {
            // 获取本地表和云端表的最大更新时间
            LocalDateTime localTableMaxTime = getLocalTableMaxTime(syncTableConfig);
            LocalDateTime cloudTableMaxTime = getCloudTableMaxTime(syncTableConfig);

            // 记录获取到的时间信息
            logTimeInfo(tableName, localTableMaxTime, cloudTableMaxTime);

            // 根据任务类型确定源表和目标表的时间
            TimeComparisonResult result = determineSourceAndTargetTimes(syncTaskType, localTableMaxTime,
                    cloudTableMaxTime);

            // 执行时间比较逻辑
            boolean needSync = performTimeComparison(result);

            // 记录比较结果
            logComparisonResult(tableName, result, needSync);

            return needSync;

        } catch (Exception e) {
            log.error("❌ 比较表 {} 最大更新时间时发生异常", tableName, e);
            // 发生异常时默认需要同步，确保数据一致性
            return true;
        }
    }

    /**
     * 获取本地表最大更新时间
     *
     * @param syncTableConfig 同步表配置
     * @return 本地表最大更新时间，可能为null
     */
    private LocalDateTime getLocalTableMaxTime(SyncTableConfigDO syncTableConfig) {
        try {
            LocalDateTime maxTime = localDataMapper.getTableMaxUpdateTime(syncTableConfig);
            log.debug("📊 本地表 {}.{} 最大更新时间: {}",
                    syncTableConfig.getSchemaName(),
                    syncTableConfig.getTableName(),
                    maxTime != null ? maxTime.format(TIME_FORMATTER) : "null");
            return maxTime;
        } catch (Exception e) {
            log.warn("⚠️ 获取本地表 {} 最大更新时间失败: {}", syncTableConfig.getTableName(), e.getMessage());
            return null;
        }
    }

    /**
     * 获取云端表最大更新时间
     *
     * @param syncTableConfig 同步表配置
     * @return 云端表最大更新时间，可能为null
     */
    private LocalDateTime getCloudTableMaxTime(SyncTableConfigDO syncTableConfig) {
        try {
            LocalDateTime maxTime = cloudDataMapper.getTableMaxUpdateTime(syncTableConfig);
            log.debug("☁️ 云端表 {}.{} 最大更新时间: {}",
                    syncTableConfig.getDataSchemaName(),
                    syncTableConfig.getDataTableName(),
                    maxTime != null ? maxTime.format(TIME_FORMATTER) : "null");
            return maxTime;
        } catch (Exception e) {
            log.warn("⚠️ 获取云端表 {} 最大更新时间失败: {}", syncTableConfig.getTableName(), e.getMessage());
            return null;
        }
    }

    /**
     * 记录时间信息
     */
    private void logTimeInfo(String tableName, LocalDateTime localTime, LocalDateTime cloudTime) {
        if (log.isDebugEnabled()) {
            log.debug("⏰ 表 {} 时间信息 - 本地: {}, 云端: {}",
                    tableName,
                    localTime != null ? localTime.format(TIME_FORMATTER) : "null",
                    cloudTime != null ? cloudTime.format(TIME_FORMATTER) : "null");
        }
    }

    /**
     * 确定源表和目标表的时间
     *
     * @param syncTaskType 同步任务类型
     * @param localTime 本地表时间
     * @param cloudTime 云端表时间
     * @return 时间比较结果对象
     */
    private TimeComparisonResult determineSourceAndTargetTimes(SyncTaskTypeEnum syncTaskType,
                                                               LocalDateTime localTime,
                                                               LocalDateTime cloudTime) {

        LocalDateTime sourceMaxTime;
        LocalDateTime targetMaxTime;
        String sourceDescription;
        String targetDescription;

        if (syncTaskType.equals(SyncTaskTypeEnum.CLOUD_TO_LOCAL)) {
            // 云到本地：云端是源，本地是目标
            sourceMaxTime = cloudTime;
            targetMaxTime = localTime;
            sourceDescription = "云端表";
            targetDescription = "本地表";
        } else if (syncTaskType.equals(SyncTaskTypeEnum.LOCAL_TO_CLOUD)) {
            // 本地到云：本地是源，云端是目标
            sourceMaxTime = localTime;
            targetMaxTime = cloudTime;
            sourceDescription = "本地表";
            targetDescription = "云端表";
        } else {
            log.warn("⚠️ 未知的同步任务类型: {}", syncTaskType);
            // 默认处理
            sourceMaxTime = cloudTime;
            targetMaxTime = localTime;
            sourceDescription = "源表";
            targetDescription = "目标表";
        }

        return new TimeComparisonResult(sourceMaxTime, targetMaxTime, sourceDescription, targetDescription);
    }

    /**
     * 执行时间比较逻辑
     *
     * @param result 时间比较结果对象
     * @return true-需要同步，false-不需要同步
     */
    private boolean performTimeComparison(TimeComparisonResult result) {
        LocalDateTime sourceMaxTime = result.sourceMaxTime();
        LocalDateTime targetMaxTime = result.targetMaxTime();

        // 如果源表和目标表时间都不为null，进行时间比较
        if (sourceMaxTime != null && targetMaxTime != null) {
            return sourceMaxTime.isAfter(targetMaxTime);
        } else {
            // 如果任一时间为null，默认需要同步
            log.debug("🔄 时间比较 - 存在null值，默认需要同步");
            return true;
        }
    }

    /**
     * 记录比较结果
     */
    private void logComparisonResult(String tableName, TimeComparisonResult result, boolean needSync) {
        if (needSync) {
            log.info("✅ 表 {} 需要同步 - {} 时间({}) 晚于 {} 时间({})",
                    tableName,
                    result.sourceDescription(),
                    result.sourceMaxTime() != null ? result.sourceMaxTime().format(TIME_FORMATTER) : "null",
                    result.targetDescription(),
                    result.targetMaxTime() != null ? result.targetMaxTime().format(TIME_FORMATTER) : "null");
        } else {
            log.info("⏭️ 表 {} 无需同步 - {} 时间({}) 不晚于 {} 时间({})",
                    tableName,
                    result.sourceDescription(),
                    result.sourceMaxTime() != null ? result.sourceMaxTime().format(TIME_FORMATTER) : "null",
                    result.targetDescription(),
                    result.targetMaxTime() != null ? result.targetMaxTime().format(TIME_FORMATTER) : "null");
        }
    }

    // ═══════════════════════════════════════════════════════════════════════════════════════
    // 🔧 内部类 - 时间比较结果
    // ═══════════════════════════════════════════════════════════════════════════════════════

    /**
     * 时间比较结果内部类
     *
     * <p>封装时间比较过程中的相关信息，便于传递和处理</p>
     */
    private record TimeComparisonResult(LocalDateTime sourceMaxTime, LocalDateTime targetMaxTime,
                                        String sourceDescription, String targetDescription) {

    }

}
