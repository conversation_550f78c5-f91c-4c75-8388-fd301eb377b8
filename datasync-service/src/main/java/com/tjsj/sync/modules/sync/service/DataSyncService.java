package com.tjsj.sync.modules.sync.service;

import com.tjsj.common.enums.sync.SyncTypeEnum;
import com.tjsj.modules.log.model.entity.DatabaseSyncHistoryDO;
import com.tjsj.modules.sync.model.entity.DataSyncManualDO;
import com.tjsj.sync.modules.sync.model.entity.SyncTableConfigDO;
import org.jetbrains.annotations.NotNull;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface DataSyncService {

	/**
     * 自动同步数据
     *
     * @param ifInsert  是否插入
     * @param configIds 配置id列表
     * <AUTHOR> Ye
     * @date 2024/08/07
     */
	void syncCloudToLocal(Boolean ifInsert, List<Integer> configIds);

	/**
	 * 同步单一表数据
	 *
	 * @param taskId          任务id
	 * @param syncTableConfig 同步表配置
	 * @param ifInsert        是否插入数据到目标数据库
	 * @param syncTypeEnum    定时任务类型
	 * <AUTHOR> Ye
	 * @date 2024/12/17
	 */
	void syncSingleTableData(Integer taskId, SyncTableConfigDO syncTableConfig, Boolean ifInsert,
	                         SyncTypeEnum syncTypeEnum);


	/**
	 * 过滤表中的列
	 * <p>过滤调用数据源表中存在，但是目标表不存在的字段</p>
	 *
	 * @param tableConfig       表配置
	 * @param dataList          数据列表
	 * @param syncHistoryRecord 同步历史记录
	 * <AUTHOR> Ye
	 * @date 2024/12/17
	 */
	void filterTableColumns(@NotNull SyncTableConfigDO tableConfig, List<Map<String, Object>> dataList,
	                        DatabaseSyncHistoryDO syncHistoryRecord);

	/**
	 * 过滤数据列
	 *
	 * @param tableConfig    表配置
	 * @param insertDataList 插入数据列表
	 * <AUTHOR> Ye
	 * @date 2025/04/08
	 */
	void filterDataColumns(@NotNull SyncTableConfigDO
			                       tableConfig, List<Map<String, Object>> insertDataList);

	/**
	 * 手动同步阿里云数据库数据到本地数据库
	 *
	 * <AUTHOR> Ye
	 * @date 2024/07/10
	 */
	void syncManual();


	/**
	 * 转移本地同步手动到云
	 *
	 * <AUTHOR> Ye
	 * @date 2024/10/29
	 */
	void transferLocalSyncManualToCloud();


	/**
	 * 手动同步单一表数据
	 *
	 * @param taskId          任务id
	 * @param syncTableConfig 同步表配置
	 * <AUTHOR> Ye
	 * @date 2025/02/05
	 */
	void manualSyncSingleTableData(Integer taskId, DataSyncManualDO syncTableConfig);


	/**
	 * 比较表最大值时间
	 *
	 * @param syncTableConfig 同步表配置
	 * @return {@link Boolean }
	 * <AUTHOR> Ye
	 * @date 2025/02/05
	 */
	Boolean compareTableMaxTime(SyncTableConfigDO syncTableConfig);


	/**
	 * 比较数据hash值
	 *
	 * @param insertDataList  插入数据列表
	 * @param syncTableConfig 同步表配置
	 * @param syncTypeEnum
	 * @return {@link Boolean }
	 * <AUTHOR> Ye
	 * @date 2025/05/21
	 */
	Boolean checkDataHashValue(List<Map<String, Object>> insertDataList, SyncTableConfigDO syncTableConfig, SyncTypeEnum syncTypeEnum);

}
