package com.tjsj.sync.modules.sync.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tjsj.datasource.DataSourceNames;
import com.tjsj.sync.modules.sync.model.LocalDatabaseSyncManual;
import org.apache.ibatis.annotations.Mapper;

/**
 * CloudDatabaseSyncManualMapper
 *
 * <AUTHOR>
 * @date 2024/08/07
 * @description 针对表【t_database_sync_info(数据库同步指定表)】的数据库操作Mapper
 */
@Mapper
@DS(DataSourceNames.TARGET_DB)
public interface LocalDatabaseSyncManualMapper extends BaseMapper<LocalDatabaseSyncManual> {

}




