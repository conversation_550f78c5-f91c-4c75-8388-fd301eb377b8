package com.tjsj.sync.modules.sync.component.processor;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.tjsj.common.enums.TaskExecuteStatusEnum;
import com.tjsj.common.enums.base.CommonStatus;
import com.tjsj.common.enums.sync.SyncTypeEnum;
import com.tjsj.modules.log.model.entity.DatabaseSyncHistoryDO;
import com.tjsj.modules.log.service.DatabaseSyncHistoryService;
import com.tjsj.modules.log.service.LocalDatabaseSyncHistoryService;
import com.tjsj.sync.modules.sync.model.entity.SyncTableConfigDO;
import com.tjsj.sync.modules.sync.service.SyncTableConfigService;
import com.tjsj.sync.modules.sync.model.bo.SyncContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 同步后置处理器
 * 
 * <p>负责数据同步完成后的后置处理工作，是数据同步流程的最后一个环节。
 * 该处理器确保同步过程的完整性，包括历史记录保存、状态更新和统计信息记录。</p>
 * 
 * <h3>🎯 主要功能</h3>
 * <ul>
 *   <li><strong>历史记录保存</strong>：将同步历史记录保存到云端和本地数据库</li>
 *   <li><strong>状态更新</strong>：更新表配置的同步状态，释放同步锁</li>
 *   <li><strong>统计信息记录</strong>：记录同步耗时、数据量等统计信息</li>
 *   <li><strong>哈希值更新</strong>：更新Quartz定时任务的数据哈希值</li>
 *   <li><strong>异常情况处理</strong>：处理同步成功和失败的不同情况</li>
 * </ul>
 * 
 * <h3>🔄 处理流程</h3>
 * <ol>
 *   <li>检查是否需要保存历史记录（根据同步类型和数据量判断）</li>
 *   <li>计算同步耗时和设置结束时间</li>
 *   <li>设置同步执行状态（成功/失败）</li>
 *   <li>保存历史记录到云端和本地数据库</li>
 *   <li>更新表配置状态和哈希值</li>
 *   <li>释放同步锁，允许下次同步</li>
 * </ol>
 * 
 * <h3>⚡ 性能特性</h3>
 * <ul>
 *   <li>异步保存历史记录，不阻塞主流程</li>
 *   <li>批量更新操作，提高数据库性能</li>
 *   <li>智能判断是否需要保存记录，避免冗余操作</li>
 * </ul>
 * 
 * <AUTHOR> Ye
 * @version 1.0.0
 * @since 2025-08-01
 * @see SyncContext
 * @see DatabaseSyncHistoryDO
 * @see SyncTableConfigDO
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class SyncPostProcessor {

    /**
     * 数据库同步历史服务（云端）
     */
    private final DatabaseSyncHistoryService databaseSyncHistoryService;
    
    /**
     * 本地数据库同步历史服务
     */
    private final LocalDatabaseSyncHistoryService localDatabaseSyncHistoryService;
    
    /**
     * 同步表配置服务
     */
    private final SyncTableConfigService syncTableConfigService;


    /**
     * 处理同步后置操作
     * 
     * <p>该方法是后置处理器的核心方法，负责完成数据同步后的所有收尾工作。
     * 包括历史记录保存、状态更新、统计信息记录等。</p>
     * 
     * <h4>🔍 处理逻辑</h4>
     * <ul>
     *   <li><strong>条件判断</strong>：根据同步类型和数据量判断是否需要保存记录</li>
     *   <li><strong>历史记录处理</strong>：保存详细的同步历史信息</li>
     *   <li><strong>状态更新</strong>：更新表配置状态，释放同步锁</li>
     *   <li><strong>异常处理</strong>：确保即使出现异常也能正确更新状态</li>
     * </ul>
     * 
     * <h4>⚠️ 特殊处理</h4>
     * <ul>
     *   <li>Quartz定时任务且数据量为0时，不保存历史记录</li>
     *   <li>同步失败时，记录详细的错误信息</li>
     *   <li>更新Quartz任务的数据哈希值</li>
     * </ul>
     * 
     * @param context 同步上下文，包含所有同步过程信息
     * @throws RuntimeException 如果后置处理过程中发生不可恢复的错误
     */
    public void process(SyncContext context) {
        // 参数验证
        if (context == null) {
            log.error("❌ 同步上下文不能为null");
            throw new IllegalArgumentException("同步上下文不能为null");
        }
        
        String tableName = context.getFullTableName();
        log.debug("🔄 开始后置处理 - 表: {}", tableName);
        
        try {
            // 1. 判断是否需要保存历史记录
            if (shouldSaveHistoryRecord(context)) {
                saveHistoryRecord(context);
            } else {
                log.debug("⏭️ 跳过历史记录保存 - 表: {} (Quartz同步且无数据变化)", tableName);
            }
            
            // 2. 更新表配置状态
            updateTableConfigStatus(context);
            
            // 3. 记录处理完成日志
            logProcessComplete(context);
            
        } catch (Exception e) {
            log.error("❌ 后置处理失败 - 表: {}", tableName, e);
            // 即使后置处理失败，也要尝试释放同步锁
            try {
                releaseSyncLock(context);
            } catch (Exception lockException) {
                log.error("❌ 释放同步锁失败 - 表: {}", tableName, lockException);
            }
            throw new RuntimeException("后置处理失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 判断是否需要保存历史记录
     * 
     * <p>根据同步类型和数据量判断是否需要保存历史记录。
     * Quartz定时任务如果没有数据变化，则不保存历史记录以减少存储开销。</p>
     * 
     * @param context 同步上下文
     * @return true-需要保存，false-不需要保存
     */
    private boolean shouldSaveHistoryRecord(SyncContext context) {
        // 非Quartz同步，总是保存历史记录
        if (!Objects.equals(context.getSyncTypeEnum(), SyncTypeEnum.QUARTZ)) {
            return true;
        }
        
        // Quartz同步，只有当有数据变化时才保存历史记录
        return context.getTotalDataNum() > 0;
    }
    
    /**
     * 保存同步历史记录
     * 
     * <p>将同步历史记录保存到云端和本地数据库，记录详细的同步信息。</p>
     * 
     * @param context 同步上下文
     */
    private void saveHistoryRecord(SyncContext context) {
        DatabaseSyncHistoryDO syncHistory = context.getSyncHistory();
        if (syncHistory == null) {
            log.warn("⚠️ 同步历史记录为null，跳过保存 - 表: {}", context.getFullTableName());
            return;
        }
        
        try {
            // 计算同步耗时
            long duration = context.getDuration();
            LocalDateTime endTime = LocalDateTime.now();
            
            // 设置历史记录属性
            syncHistory.setTimeDuration((int) duration)
                      .setDataNum(context.getTotalDataNum())
                      .setEndTime(endTime);
            
            // 设置执行状态（如果未设置则默认为成功）
            if (syncHistory.getTaskExecuteStatus() == null) {
                syncHistory.setTaskExecuteStatus(TaskExecuteStatusEnum.SUCCESS);
            }
            
            // 保存到云端和本地数据库
            databaseSyncHistoryService.save(syncHistory);
            localDatabaseSyncHistoryService.save(syncHistory);
            
            log.debug("📋 历史记录保存成功 - 表: {}, 耗时: {}ms, 数据量: {} 条", 
                context.getFullTableName(), duration, context.getTotalDataNum());
                
        } catch (Exception e) {
            log.error("❌ 保存历史记录失败 - 表: {}", context.getFullTableName(), e);
            throw new RuntimeException("保存历史记录失败", e);
        }
    }
    
    /**
     * 更新表配置状态
     * 
     * <p>更新同步表配置的状态信息，包括同步状态和Quartz哈希值。</p>
     * 
     * @param context 同步上下文
     */
    private void updateTableConfigStatus(SyncContext context) {
        try {
            SyncTableConfigDO tableConfig = context.getSyncTableConfig();
            
            // 构建更新条件
            var updateWrapper = Wrappers.<SyncTableConfigDO>lambdaUpdate()
                .set(SyncTableConfigDO::getIfSyncing, CommonStatus.DISABLE)
                .eq(SyncTableConfigDO::getId, tableConfig.getId());
            
            // 如果有Quartz哈希值，一并更新
            String quartzHash = tableConfig.getQuartzLastSyncDataHash();
            if (quartzHash != null) {
                updateWrapper.set(SyncTableConfigDO::getQuartzLastSyncDataHash, quartzHash);
                log.debug("🔐 更新Quartz哈希值 - 表: {}", context.getFullTableName());
            }
            
            // 执行更新
            syncTableConfigService.update(updateWrapper);
            
            log.debug("📝 表配置状态更新成功 - 表: {} (同步状态: 未同步中)", context.getFullTableName());
            
        } catch (Exception e) {
            log.error("❌ 更新表配置状态失败 - 表: {}", context.getFullTableName(), e);
            throw new RuntimeException("更新表配置状态失败", e);
        }
    }
    
    /**
     * 释放同步锁
     * 
     * <p>在异常情况下释放同步锁，确保表不会被永久锁定。</p>
     * 
     * @param context 同步上下文
     */
    private void releaseSyncLock(SyncContext context) {
        try {
            syncTableConfigService.update(Wrappers.<SyncTableConfigDO>lambdaUpdate()
                .set(SyncTableConfigDO::getIfSyncing, CommonStatus.DISABLE)
                .eq(SyncTableConfigDO::getId, context.getSyncTableConfig().getId()));
                
            log.info("🔓 已释放同步锁 - 表: {}", context.getFullTableName());
        } catch (Exception e) {
            log.error("❌ 释放同步锁异常 - 表: {}", context.getFullTableName(), e);
        }
    }
    
    /**
     * 记录处理完成日志
     *
     * @param context 同步上下文
     */
    private void logProcessComplete(SyncContext context) {
        long duration = context.getDuration();
        int dataNum = context.getTotalDataNum();

        if (context.getSyncHistory() != null &&
            context.getSyncHistory().getTaskExecuteStatus() == TaskExecuteStatusEnum.FAILURE) {
            log.info("⚠️ 后置处理完成 - 表: {} (同步失败), 耗时: {}ms",
                context.getFullTableName(), duration);
        } else {
            log.info("✅ 后置处理完成 - 表: {} (同步成功), 耗时: {}ms, 数据量: {} 条",
                context.getFullTableName(), duration, dataNum);
        }
    }

    // ═══════════════════════════════════════════════════════════════════════════════════════
    // 📝 使用示例和最佳实践
    // ═══════════════════════════════════════════════════════════════════════════════════════

    /*
     * 使用示例：
     *
     * @Component
     * public class DataSyncServiceImpl {
     *
     *     private final SyncPostProcessor postProcessor;
     *
     *     public void syncSingleTableData(...) {
     *         SyncContext context = SyncContext.builder()...build();
     *
     *         try {
     *             // 前置检查、准备、同步等操作
     *             preChecker.checkSyncable(context);
     *             preparer.prepare(context);
     *             synchronizer.synchronize(context);
     *
     *             // 后置处理
     *             postProcessor.process(context);
     *
     *         } catch (Exception e) {
     *             // 异常处理
     *             exceptionHandler.handle(context, e);
     *             // 仍需要进行后置处理以释放锁
     *             postProcessor.process(context);
     *         }
     *     }
     * }
     *
     * 最佳实践：
     * 1. 总是在同步流程的最后调用后置处理器
     * 2. 即使同步失败，也要调用后置处理器释放锁
     * 3. 确保SyncContext包含完整的同步信息
     * 4. 处理后置处理器可能抛出的异常
     * 5. 监控历史记录保存的成功率
     * 6. 定期检查是否有被永久锁定的表
     *
     * 性能优化建议：
     * 1. 考虑异步保存历史记录以提高性能
     * 2. 批量更新多个表的状态
     * 3. 使用数据库连接池优化数据库操作
     * 4. 监控数据库操作的执行时间
     */
}
