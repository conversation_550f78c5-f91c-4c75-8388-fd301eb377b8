package com.tjsj.sync.modules.sync.mapper;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.tjsj.common.enums.sync.SyncTypeEnum;
import com.tjsj.datasource.DataSourceNames;
import com.tjsj.modules.base.model.vo.TypeCountVO;
import com.tjsj.modules.sync.model.entity.CloudDataSyncManualDO;
import com.tjsj.modules.sync.model.entity.SyncTableConfigDO;
import com.tjsj.sync.modules.business.model.FinancialBaseModel;
import com.tjsj.sync.modules.tailored.xyzq.model.entity.LocalXyzqBdzqDO;
import com.tjsj.sync.modules.tailored.xyzq.model.entity.LocalXyzqMrgTrdFlagDO;
import com.tjsj.sync.modules.tailored.xyzq.model.entity.LocalXyzqZslDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * LocalDataMapper
 *
 * <AUTHOR> Ye
 * @date 2024/08/07
 * @description 本地数据映射器
 */
@Mapper
@DS(DataSourceNames.TARGET_DB)
public interface LocalDataMapper {

	List<Map<String, Object>> test();


	/**
	 * 获取表数据最大值id
	 *
	 * @param table 表
	 * @return {@link Long }
	 * <AUTHOR> Ye
	 * @date 2024/08/07
	 */
	Long getTableDataMaxId(@Param("table") SyncTableConfigDO table);


	/**
	 * 插入表数据
	 *
	 * @param table     表
	 * @param cloudData 云数据
	 * <AUTHOR> Ye
	 * @date 2024/08/07
	 */
	void insertTableData(@Param("table") SyncTableConfigDO table, @Param("data") Map<String, Object> cloudData);


	/**
	 * 获取表最大值更新时间
	 *
	 * @param tableConfig 表配置
	 * @return {@link Date }
	 * <AUTHOR> Ye
	 * @date 2024/08/03
	 */
	LocalDateTime getTableMaxUpdateTime(@Param("tableConfig") SyncTableConfigDO tableConfig);

	/**
	 * 批量插入表数据，对主键重复数据进行更新操作
	 *
	 * @param tableConfig 表配置
	 * @param dataList    数据列表
	 * <AUTHOR> Ye
	 * @date 2024/07/11
	 */
	void batchInsertTableData(@Param("tableConfig") SyncTableConfigDO tableConfig,
	                          @Param("dataList") List<Map<String, Object>> dataList);

	/**
	 * 删除表数据通过条件
	 *
	 * @param deleteTableName       删除表名称
	 * @param deleteTableCondition  删除表条件
	 * @param deleteStartId         删除开始id
	 * @param deleteStartUpdateTime 删除开始更新时间
	 * <AUTHOR> Ye
	 * @date 2025/02/05
	 */
	void deleteTableDataByCondition(@Param("deleteTableName") String deleteTableName,
	                                @Param("deleteTableCondition") String deleteTableCondition,
	                                @Param("deleteStartId") Integer deleteStartId,
	                                @Param("deleteStartUpdateTime") LocalDateTime deleteStartUpdateTime);

	/**
	 * 根据条件批量插入数据
	 *
	 * @param syncInfo  同步信息参数
	 * @param cloudData 同步数据
	 */
	void batchInsertTableDataByCondition(@Param("syncInfo") CloudDataSyncManualDO syncInfo,
	                                     @Param("dataList") List<Map<String, Object>> cloudData);

	/**
	 * 截断表数据
	 *
	 * @param deleteTableName 同步信息
	 * <AUTHOR> Ye
	 * @date 2024/07/30
	 */
	void truncateTableData(@Param("deleteTableName") String deleteTableName);

	/**
	 * truncate被同步表
	 *
	 * @param tableConfig 表配置
	 * <AUTHOR> Ye
	 * @date 2024/10/16
	 */
	void truncateInsertTableData(@Param("tableConfig") SyncTableConfigDO tableConfig);

	/**
	 * 执行sql到本地
	 *
	 * @param executeSql 执行sql
	 * <AUTHOR> Ye
	 * @date 2024/07/30
	 */
	void executeSqlToLocal(@Param("executeSql") String executeSql);

	/**
	 * 选择表数据通过条件
	 *
	 * @param syncInfo  同步信息
	 * @param offset    抵消
	 * @param batchSize 批量大小
	 * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
	 * <AUTHOR> Ye
	 * @date 2024/07/30
	 */
	List<Map<String, Object>> selectTableDataByCondition(@Param("syncInfo") CloudDataSyncManualDO syncInfo,
	                                                     @Param("offset") int offset,
	                                                     @Param("batchSize") int batchSize);

	/**
	 * 列表表列
	 *
	 * @param schemaName 架构名称
	 * @param tableName  表名称
	 * @return {@link List }<{@link String }>
	 * <AUTHOR> Ye
	 * @date 2024/10/10
	 */
	List<String> listTableColumns(@Param("schemaName") String schemaName, @Param("tableName") String tableName);

	/**
	 * 根据数据库同步表查询数据
	 *
	 * @param tableConfig 表配置
	 * @param updateTime  更新时间
	 * @param offset      偏移量
	 * @param batchSize   批量大小
	 * @param syncType    同步类型
	 * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
	 * <AUTHOR> Ye
	 * @date 2024/08/01
	 */
	List<Map<String, Object>> selectTableDataByDataRange(@Param("tableConfig") SyncTableConfigDO tableConfig,
	                                                     @Param("updateTime") LocalDateTime updateTime,
	                                                     @Param("offset") int offset,
	                                                     @Param("batchSize") int batchSize,
	                                                     @Param("syncType") SyncTypeEnum syncType);


	/**
	 * 获取财务表数据统计
	 *
	 * @param schemaName         架构名称
	 * @param tableName          表名称
	 * @param tableGroupByColumn 表分组字段
	 * @return {@link List }<{@link FinancialBaseModel }>
	 * <AUTHOR> Ye
	 * @date 2025/04/08
	 */
	List<TypeCountVO> getTableGroupDataCount(@Param("schemaName") String schemaName,
	                                         @Param("tableName") String tableName,
	                                         @Param("tableGroupByColumn") String tableGroupByColumn);


	/**
	 * 获取财务表数据通过股票
	 *
	 * @param filteredGroupByColumnValue 一级分组字段值
	 * @param schemaName                 架构名称
	 * @param tableName                  表名称
	 * @param tableGroupByColumnTierOne  一级分组字段
	 * @param tableGroupByColumnTierTwo  二级分组字段
	 * @return {@link List }<{@link FinancialBaseModel }>
	 * <AUTHOR> Ye
	 * @date 2025/04/08
	 */
	List<TypeCountVO> getTableGroupByDataCountTierTwo(@Param("filteredGroupByColumnValue") String filteredGroupByColumnValue,
	                                                  @Param("schemaName") String schemaName,
	                                                  @Param("tableName") String tableName,
	                                                  @Param("tableGroupByColumnTierOne") String tableGroupByColumnTierOne,
	                                                  @Param("tableGroupByColumnTierTwo") String tableGroupByColumnTierTwo);

	/**
	 * 获取表字段名称和值列表
	 *
	 * @param schemaName 架构名称
	 * @param tableName  表名称
	 * @return {@link List }<{@link String }>
	 * <AUTHOR> Ye
	 * @date 2025/05/08
	 */
	List<String> getFieldNameValueList(@Param("schemaName") String schemaName, @Param("tableName") String tableName);

	/**
	 * 删除冗余字段数据
	 *
	 * @param schemaName             架构名称
	 * @param tableName              表名称
	 * @param redundantFiledNameList 冗余字段名称列表
	 * <AUTHOR> Ye
	 * @date 2025/05/08
	 */
	void deleteRedundantFiledNameData(@Param("schemaName") String schemaName, @Param("tableName") String tableName,
	                                  @Param("redundantFiledNameList") List<String> redundantFiledNameList);

	/**
	 * 插入批量兴业证券-标的证券
	 *
	 * @param list 列表
	 *
	 * <AUTHOR> Ye
	 * @date 2025/07/01
	 */
	void insertBatchXyzqBdzq(@Param("list") List<LocalXyzqBdzqDO> list);

	/**
	 * 插入批量兴业证券-担保证券
	 *
	 * @param list 列表
	 *
	 * <AUTHOR> Ye
	 * @date 2025/07/01
	 */
	void insertBatchXyzqZsl(@Param("list") List<LocalXyzqZslDO> list);

	/**
	 * 插入批量兴业证券-采集标志
	 *
	 * @param list 列表
	 *
	 * <AUTHOR> Ye
	 * @date 2025/07/01
	 */
	void insertBatchXyzqMrgTrdFlag(@Param("list") List<LocalXyzqMrgTrdFlagDO> list);

	/**
	 * 测试删除分布锁
	 *
	 *
	 * @return int
	 * <AUTHOR> Ye
	 * @date 2025/07/02
	 */
	int testDeleteDistributedLock();

	/**
	 * 删除兴业证券-采集标志
	 *
	 * @param increRecordIdList 增量记录id列表
	 *
	 * <AUTHOR> Ye
	 * @date 2025/07/01
	 */
	void removeXyzqMrgTrdFlagByIds(@Param("increRecordIdList") List<Integer> increRecordIdList);

}
