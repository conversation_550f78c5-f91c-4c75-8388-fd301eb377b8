package com.tjsj.sync.modules.business.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.tjsj.common.config.TarkinConfig;
import com.tjsj.common.enums.TaskExecuteStatusEnum;
import com.tjsj.common.enums.base.CommonStatus;
import com.tjsj.common.enums.sync.SyncTypeEnum;
import com.tjsj.modules.base.model.vo.TypeCountVO;
import com.tjsj.modules.log.model.entity.DatabaseSyncHistoryDO;
import com.tjsj.modules.log.service.DatabaseSyncHistoryService;
import com.tjsj.modules.log.service.LocalDatabaseSyncHistoryService;
import com.tjsj.modules.sync.model.entity.SyncTableConfigDO;
import com.tjsj.modules.sync.service.SyncTableConfigService;
import com.tjsj.sync.modules.business.model.FinancialBaseModel;
import com.tjsj.sync.modules.business.service.BusinessSyncService;
import com.tjsj.sync.modules.business.service.InfoSchemaColumnService;
import com.tjsj.sync.modules.sync.mapper.CloudDataMapper;
import com.tjsj.sync.modules.sync.mapper.LocalDataMapper;
import com.tjsj.sync.modules.sync.service.DataSyncService;
import com.tjsj.sync.modules.sync.utils.MySqlKeyword;
import com.tjsj.sync.modules.sync.utils.constants.SyncConsts;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * BusinessSyncServiceImpl
 *
 * <AUTHOR> Ye
 * @version 1.0.0
 * @date 2025/4/8 13:38
 * @description 业务数据同步服务实现类
 */
@Service
@Slf4j
public class BusinessSyncServiceImpl implements BusinessSyncService {
    @Resource
    private InfoSchemaColumnService infoSchemaColumnService;
    @Resource
    private DatabaseSyncHistoryService databaseSyncHistoryService;
    @Resource
    private LocalDatabaseSyncHistoryService localDatabaseSyncHistoryService;

    @Resource
    private DataSyncService dataSyncService;

    @Resource
    private SyncTableConfigService syncTableConfigService;

    @Resource
    private CloudDataMapper cloudDataMapper;

    @Resource
    private LocalDataMapper localDataMapper;

    @Resource
    private TarkinConfig tarkinConfig;

    /**
     * 每批拉取数据量
     */
    @Value("${sync.financial-table-fix.pull-batch-size:10000}")
    private Integer pullBatchSize;

    /**
     * 每批插入数据量
     */
    @Value("${sync.financial-table-fix.insert-batch-size:10000}")
    private Integer insertBatchSize;

    @Override
    public void syncFixData() {

        // #1) 先取出需要进行修复的所有表
        List<SyncTableConfigDO> syncTableConfigList = this.getSyncFixDataTable();

        // #2) 遍历所有需要修复的表，进行数据同步修复
        for (SyncTableConfigDO syncTableConfigDO : syncTableConfigList) {

            // 判断当前表是否是财务表
            boolean ifFinancialTable = syncTableConfigDO.getDataSchemaName().equals(SyncConsts.SCHEMA_PLEDGEDATA)
                    && syncTableConfigDO.getDataTableName().startsWith(SyncConsts.FINANCE_TABLE_PREFIX);

            // 如果是财务表，判断是否需要删除多余的FiledName对应的数据
            if (ifFinancialTable) {
                this.deleteFiledNameRedundantData(syncTableConfigDO);
            }

            List<String> tableGroupByColumnList = Arrays.asList(syncTableConfigDO.getTableGroupByColumn().split(","));
            // #2.1) 判断当前表分组结果 本地库与云库的对应的数据量是否一致
            Map<String, Long> cloudGroupByDataCountMap =
                    cloudDataMapper.getTableGroupByDataCount(syncTableConfigDO.getDataSchemaName(),
                                    syncTableConfigDO.getDataTableName(), tableGroupByColumnList.get(0))
                            .stream()
                            .collect(Collectors.toMap(TypeCountVO::getTypeName, TypeCountVO::getCount));

            Map<String, Long> localGroupByDataCountMap =
                    localDataMapper.getTableGroupDataCount(syncTableConfigDO.getSchemaName(),
                                    syncTableConfigDO.getTableName(), tableGroupByColumnList.get(0))
                            .stream()
                            .collect(Collectors.toMap(TypeCountVO::getTypeName, TypeCountVO::getCount));

            // #2.2) 保留本地库中不存在的分组标志，或者数据量不一致的分组标志
            //如：1.云库中存在date值为2024-12-31对应的数据，但是本地库中不存在
            //2.云库中date值为2024-12-31对应的数据量比本地库的数据量要大
            List<String> filteredGroupByList = cloudGroupByDataCountMap.entrySet().stream()
                    .filter(entry -> {
                        String cloudDate = entry.getKey();
                        Long cloudDateDataCount = entry.getValue();

                        if (!localGroupByDataCountMap.containsKey(cloudDate)) {
                            return true;
                        } else {
                            Long localDateDataCount = localGroupByDataCountMap.get(cloudDate);
                            return cloudDateDataCount > localDateDataCount;
                        }

                    })
                    .map(Map.Entry::getKey)
                    .toList();

            Boolean tableSyncable = syncTableConfigService.checkTableSyncable(syncTableConfigDO.getId());

            // #2.3) 如果1.当前表正在同步，则跳过当前表 2.当前表没有需要修复的数据，则跳过当前表
            if (CollUtil.isEmpty(filteredGroupByList) || !tableSyncable) {
                continue;
            }

            // #2.4) 将当前同步表配置修改为同步中状态，创建同步历史日志
            syncTableConfigService.update(Wrappers.<SyncTableConfigDO>lambdaUpdate()
                    .set(SyncTableConfigDO::getIfSyncing, CommonStatus.ENABLE)
                    .set(SyncTableConfigDO::getLastSyncStartTime, LocalDateTime.now())
                    .eq(SyncTableConfigDO::getId, syncTableConfigDO.getId()));
            DatabaseSyncHistoryDO databaseSyncHistory =
                    databaseSyncHistoryService.createSyncHistoryRecord(null, null, syncTableConfigDO,
                            SyncTypeEnum.TABLE_REPAIR);


            long thisTableSyncStartTime = System.currentTimeMillis();
            try {
                if (tableGroupByColumnList.size() == 1) {
                    // 根据一级表数据分组，进行表数据同步修复
                    for (String filteredGroupByColumnValue : filteredGroupByList) {

                        doFixTableData(syncTableConfigDO, filteredGroupByColumnValue, null,
                                databaseSyncHistory, tableGroupByColumnList);
                    }

                } else {

                    // 进行二级表数据分组
                    for (String filteredGroupByColumnValue : filteredGroupByList) {

                        fixTableDataGroupByTierTwo(syncTableConfigDO, filteredGroupByColumnValue,
                                databaseSyncHistory, tableGroupByColumnList);
                    }
                }

            } catch (Exception e) {

                // 在同步数据的过程中报错，记录错误日志和错误信息
                String remark = databaseSyncHistory.getRemark() == null ? "" : databaseSyncHistory.getRemark() +
                        ";";
                databaseSyncHistory.setTaskExecuteStatus(TaskExecuteStatusEnum.FAILURE)
                        .setRemark(remark + e.getMessage());

            }


            // #3) 保存同步任务历史记录，并将表配置同步状态修改为同步完成
            this.afterSyncFixData(syncTableConfigDO, databaseSyncHistory, thisTableSyncStartTime);

        }

    }

    /**
     * 修复数据后操作
     *
     * @param syncTableConfigDO      同时表配置实体类
     * @param databaseSyncHistory    数据库同步历史记录
     * @param thisTableSyncStartTime 同步开始时间
     * <AUTHOR> Ye
     * @date 2025/06/30
     */
    private void afterSyncFixData(SyncTableConfigDO syncTableConfigDO, DatabaseSyncHistoryDO databaseSyncHistory,
                                  long thisTableSyncStartTime) {

        // 保存同步任务历史记录
        databaseSyncHistory.setTaskExecuteStatus(
                        databaseSyncHistory.getTaskExecuteStatus() == null ?
                                TaskExecuteStatusEnum.SUCCESS : databaseSyncHistory.getTaskExecuteStatus())
                .setEndTime(LocalDateTime.now())
                .setTimeDuration((int) (System.currentTimeMillis() - thisTableSyncStartTime));
        databaseSyncHistoryService.save(databaseSyncHistory);
        localDatabaseSyncHistoryService.save(databaseSyncHistory);


        // 修改表配置同步状态
        syncTableConfigService.update(Wrappers.<SyncTableConfigDO>lambdaUpdate()
                .set(SyncTableConfigDO::getIfSyncing, CommonStatus.DISABLE)
                .eq(SyncTableConfigDO::getId, syncTableConfigDO.getId()));

    }

    /**
     * 删除多余的FiledName对应的数据
     *
     * @param syncTableConfigDO 配置
     * <AUTHOR> Ye
     * @date 2025/05/08
     */
    private void deleteFiledNameRedundantData(SyncTableConfigDO syncTableConfigDO) {

        List<String> tableColumns = infoSchemaColumnService.getTableColumns(syncTableConfigDO.getSchemaName(),
                syncTableConfigDO.getTableName());
        tableColumns = tableColumns.stream()
                .map(String::toLowerCase)
                .collect(Collectors.toList());
        if (tableColumns.contains(FinancialBaseModel.Fields.filedName.toLowerCase())) {
            List<String> cloudDataMapperFieldNameValueList =
                    cloudDataMapper.getFieldNameValueList(syncTableConfigDO.getDataSchemaName(),
                            syncTableConfigDO.getDataTableName());
            List<String> localDataMapperFieldNameValueList =
                    localDataMapper.getFieldNameValueList(syncTableConfigDO.getSchemaName(),
                            syncTableConfigDO.getTableName());
            if (!cloudDataMapperFieldNameValueList.isEmpty()) {
                List<String> redundantFiledNameList = localDataMapperFieldNameValueList.stream()
                        .filter(fieldNameValue -> !cloudDataMapperFieldNameValueList.contains(fieldNameValue))
                        .toList();
                if (!redundantFiledNameList.isEmpty()) {
                    // 删除本地库中多余的FiledName对应的数据
                    localDataMapper.deleteRedundantFiledNameData(syncTableConfigDO.getSchemaName(),
                            syncTableConfigDO.getTableName(), redundantFiledNameList);
                }
            }

        }


    }


    /**
     * 根据日期修复数据
     *
     * @param syncTableConfigDO          配置
     * @param filteredGroupByColumnValue 财务日期
     * @param databaseSyncHistory        已修复的数据量
     * @param tableGroupByColumnList     表分组字段列表
     * <AUTHOR> Ye
     * @date 2025/04/08
     */
    private void fixTableDataGroupByTierTwo(SyncTableConfigDO syncTableConfigDO, String filteredGroupByColumnValue,
                                            DatabaseSyncHistoryDO databaseSyncHistory,
                                            List<String> tableGroupByColumnList) {

        String tableGroupByColumnTierOne = tableGroupByColumnList.get(0);
        String tableGroupByColumnTierTwo = tableGroupByColumnList.get(1);

        // 保留本地库中不存在的股票数据，或者数据量不一致的股票数据
        Map<String, Long> cloudStockDataCountMap =
                cloudDataMapper.getTableGroupByDataCountTierTwo(filteredGroupByColumnValue,
                                syncTableConfigDO.getDataSchemaName(), syncTableConfigDO.getDataTableName(),
                                tableGroupByColumnTierOne, tableGroupByColumnTierTwo)
                        .stream()
                        .collect(Collectors.toMap(TypeCountVO::getTypeName, TypeCountVO::getCount));
        Map<String, Long> localStockDataCountMap =
                localDataMapper.getTableGroupByDataCountTierTwo(filteredGroupByColumnValue,
                                syncTableConfigDO.getSchemaName(), syncTableConfigDO.getTableName(),
                                tableGroupByColumnTierOne, tableGroupByColumnTierTwo)
                        .stream()
                        .collect(Collectors.toMap(TypeCountVO::getTypeName, TypeCountVO::getCount));

        List<String> filterStockList = cloudStockDataCountMap.entrySet().stream()
                .filter(entry -> {
                    String stockId = entry.getKey();
                    Long cloudStockDataCount = entry.getValue();

                    if (!localStockDataCountMap.containsKey(stockId)) {
                        return true;
                    } else {
                        Long localStockDataCount = localStockDataCountMap.get(stockId);
                        return cloudStockDataCount > localStockDataCount;
                    }

                })
                .map(Map.Entry::getKey)
                .toList();

        for (String stockId : filterStockList) {

            doFixTableData(syncTableConfigDO, filteredGroupByColumnValue, stockId, databaseSyncHistory,
                    tableGroupByColumnList);
        }

    }

    /**
     * 获取需要修复的同步表配置
     *
     * @return {@link List }<{@link SyncTableConfigDO }>
     * <AUTHOR> Ye
     * @date 2025/04/29
     */
    private List<SyncTableConfigDO> getSyncFixDataTable() {

        List<SyncTableConfigDO> financialTableConfigList =
                new ArrayList<>(syncTableConfigService.list(Wrappers.<SyncTableConfigDO>lambdaQuery()
                                .eq(SyncTableConfigDO::getEnableStatus, CommonStatus.ENABLE)
                                .eq(SyncTableConfigDO::getDeleteStatus, CommonStatus.ENABLE)
                                .eq(SyncTableConfigDO::getProjectId, tarkinConfig.getProjectId())
                                .eq(SyncTableConfigDO::getDbType, tarkinConfig.getDbType())
                                .eq(SyncTableConfigDO::getProfileType, tarkinConfig.getProfile())
                                .eq(SyncTableConfigDO::getSchemaName, SyncConsts.SCHEMA_PLEDGEDATA)
                                .like(SyncTableConfigDO::getTableName, SyncConsts.FINANCE_TABLE_PREFIX)
                                .orderByAsc(SyncTableConfigDO::getSyncOrder))
                        .stream()
                        .filter(syncTableConfig -> {
                            // 表名中包含数字
                            String tableName = syncTableConfig.getTableName();
                            return tableName.matches(".*\\d+.*");
                        })
                        .toList());

        List<SyncTableConfigDO> tableConfigDOList =
                syncTableConfigService.list(Wrappers.<SyncTableConfigDO>lambdaQuery()
                        .eq(SyncTableConfigDO::getEnableStatus, CommonStatus.ENABLE)
                        .eq(SyncTableConfigDO::getDeleteStatus, CommonStatus.ENABLE)
                        .eq(SyncTableConfigDO::getProjectId, tarkinConfig.getProjectId())
                        .eq(SyncTableConfigDO::getDbType, tarkinConfig.getDbType())
                        .eq(SyncTableConfigDO::getProfileType, tarkinConfig.getProfile())
                        .isNotNull(SyncTableConfigDO::getTableGroupByColumn));

        financialTableConfigList.addAll(tableConfigDOList);

        return financialTableConfigList;
    }

    /**
     * 根据日期同步数据
     *
     * @param config                    配置
     * @param groupByTierOneColumnValue 表一级分组字段值
     * @param groupByTierTwoColumnValue 表二级分组字段值
     * @param databaseSyncHistory       数据库同步历史记录
     * <AUTHOR> Ye
     * @date 2025/04/08
     */
    private void doFixTableData(SyncTableConfigDO config, String groupByTierOneColumnValue,
                                String groupByTierTwoColumnValue,
                                DatabaseSyncHistoryDO databaseSyncHistory,
                                List<String> tableGroupByColumnList) {

        // 初始偏移量
        int offset = 0;

        while (true) {
            // 拉取数据
            List<Map<String, Object>> cloudRawData = cloudDataMapper.selectFinancialTableDataByOffset(
                    groupByTierOneColumnValue, groupByTierTwoColumnValue, config.getDataSchemaName(),
                    config.getDataTableName(), offset, pullBatchSize, tableGroupByColumnList);

            if (CollUtil.isEmpty(cloudRawData)) {
                break;
            }

            offset += pullBatchSize;
            databaseSyncHistory.setDataNum((databaseSyncHistory.getDataNum() == null ? 0 :
                    databaseSyncHistory.getDataNum()) + cloudRawData.size());

            dataSyncService.filterTableColumns(config, cloudRawData, null);
            dataSyncService.filterDataColumns(config, cloudRawData);

            List<Map<String, Object>> insertList = cloudRawData.stream()
                    .map(MySqlKeyword::processKeywords)
                    .toList();

            for (int i = 0; i < insertList.size(); i += insertBatchSize) {
                List<Map<String, Object>> batchData = insertList.subList(i, Math.min(i + insertBatchSize,
                        insertList.size()));
                if (CollUtil.isNotEmpty(batchData)) {
                    localDataMapper.batchInsertTableData(config, batchData);
                }
            }

        }

    }


}
