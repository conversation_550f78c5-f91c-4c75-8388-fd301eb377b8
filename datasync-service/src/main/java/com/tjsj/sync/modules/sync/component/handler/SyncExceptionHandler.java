package com.tjsj.sync.modules.sync.component.handler;


import com.tjsj.common.enums.TaskExecuteStatusEnum;
import com.tjsj.sync.modules.sync.model.bo.SyncContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * SyncExceptionHandler
 *
 * <AUTHOR>
 * @date 2025/08/01
 * @version 1.0.0
 * @description 同步异常处理器
 */
@Component
@Slf4j
public class SyncExceptionHandler {

    /**
     * 处理同步异常
     */
    public void handle(SyncContext context, Exception e) {
        log.error("❌ 同步异常处理 - 表: {}", context.getTableName(), e);

        // 更新同步历史记录
        if (context.getSyncHistory() != null) {
            String remark = context.getSyncHistory().getRemark();
            remark = (remark == null ? "" : remark + ";") + e.getMessage();

            context.getSyncHistory()
                    .setTaskExecuteStatus(TaskExecuteStatusEnum.FAILURE)
                    .setRemark(remark);
        }

        context.setErrorMessage(e.getMessage());
    }



}
