package com.tjsj;

import ch.qos.logback.classic.LoggerContext;
import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * DataSyncApplication
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/10/29
 * @description 数据同步应用
 */
@MapperScan({"com.tjsj.**.mapper"})
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@EnableConfigurationProperties(DataSourceProperties.class)
@EnableScheduling
@EnableAsync
@EnableAspectJAutoProxy
public class DataSyncApplication extends SpringBootServletInitializer {

    public static void main(String[] args) {
        SpringApplication.run(DataSyncApplication.class, args);
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            LoggerContext context = (LoggerContext) LoggerFactory.getILoggerFactory();
            context.stop();
        }));

    }

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        return application.sources(DataSyncApplication.class);
    }

}